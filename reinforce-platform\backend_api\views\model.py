import datetime
import os
import subprocess
import time
import shutil
from rest_framework.response import Response
from rest_framework.generics import GenericAPIView
from rest_framework.viewsets import ModelViewSet
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django.utils import timezone
from django.conf import settings
from rest_framework.decorators import action

from django_filters.rest_framework import DjangoFilterBackend
from backend_api.models.deduction import Deduction
from backend_api.models.dataset import Dataset
from backend_api.serializers.dataset import DatasetBasicSerializer

from backend_api.models.model import Model
from backend_api.models.task import Task
from backend_api.serializers.deduction import ModelDeductionSerializer
from backend_api.serializers.model import ModelDetailSerializer, ModelSerializer, ModelBasicSerializer
from utils.task_utils import create_inferrence

# 模型类型到数据集类型的映射
MODEL_TO_DATASET_TYPE_MAP = {
    'text': ['目标检测车', '野战指挥车'],  # 这里根据实际需求填写对应的model_type
    'image': ['远程精式火箭炮', '无人机'],
    'video': ['智能火控', '无人车']
}

class ModelViewSets(ModelViewSet):
    """
    声明模型资源类 
    用户操作:
        获取模型信息  
        更新模型  
        删除模型 
        创建模型
    """
    queryset = Model.objects.all()
    serializer_class = ModelSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['model_type', 'model_status']
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.request.query_params.get('fields') == 'name':
            return ModelBasicSerializer
        if self.action in ['retrieve', 'list']:
            return ModelDetailSerializer
        return ModelSerializer

    def get_queryset(self):
        user = self.request.user
        queryset = Model.objects.all()
        
        # 获取过滤参数
        name = self.request.query_params.get('name', '')
        model_type = self.request.query_params.get('model_type')
        model_status = self.request.query_params.get('model_status')
        creater_name = self.request.query_params.get('creater_name')
        start_time = self.request.query_params.get('start_time', '')
        end_time = self.request.query_params.get('end_time', '')
        ids = self.request.query_params.get('ids', '')
        
        # 按指定ID过滤
        if ids:
            try:
                # 支持单个ID或逗号分隔的多个ID
                id_list = [int(id_str.strip()) for id_str in ids.split(',') if id_str.strip()]
                if id_list:
                    queryset = queryset.filter(id__in=id_list)
            except ValueError:
                # 如果ID格式不正确，返回空查询集
                return queryset.none()
        
        # 按名称过滤（模糊匹配）
        if name:
            queryset = queryset.filter(name__icontains=name)
            
        # 按创建者名称过滤（精确匹配）
        if creater_name:
            queryset = queryset.filter(creater__username=creater_name)
        
        # 按模型类型过滤（精确匹配）
        if model_type:
            queryset = queryset.filter(model_type=model_type)
            
        # 按模型状态过滤（精确匹配）
        if model_status:
            queryset = queryset.filter(model_status=model_status)
            
        # 按创建时间范围过滤 - 查找 开始时间 <= 创建时间 <= 结束时间 的数据
        if start_time and end_time:
            try:
                # 解析开始日期，设置为当天的开始时间 00:00:00
                start_datetime = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime)
                # 解析结束日期，设置为当天的结束时间 23:59:59
                end_datetime = datetime.datetime.strptime(end_time, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime)
                # 查找在时间范围内的数据
                queryset = queryset.filter(create_time__gte=start_datetime, create_time__lte=end_datetime)
            except ValueError:
                pass
        elif start_time:
            try:
                # 只有开始时间，查找大于等于开始时间的数据
                start_datetime = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime)
                queryset = queryset.filter(create_time__gte=start_datetime)
            except ValueError:
                pass
        elif end_time:
            try:
                # 只有结束时间，查找小于等于结束时间的数据
                end_datetime = datetime.datetime.strptime(end_time, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime)
                queryset = queryset.filter(create_time__lte=end_datetime)
            except ValueError:
                pass
            
        # 如果不是管理员，只显示自己的模型
        if not user.is_staff:
            queryset = queryset.filter(creater=user.id)

        return queryset.order_by('-id')


    def create(self, request):
        # task_id = request.data.get("task_id")
        # if not task_id:
        #     return Response({"err_msg": "task_id不能为空", "code": 400}, status=HTTP_400_BAD_REQUEST)
        # task = Task.objects.filter(id=task_id).first()
        # if not task:
        #     return Response({"err_msg": f"任务 {task_id} 不存在", "code": 404}, status=HTTP_400_BAD_REQUEST)
            
        # 从请求中获取必要的字段
        name = request.data.get("name")
        desc = request.data.get("desc")
        model_type = request.data.get("model_type", "未分类")  # 默认值为"未分类"
        model_size = request.data.get("model_size")
        model_status = request.data.get("model_status", "未知")  # 默认值为"未知"
        model_file = request.data.get("model_file")
        config_file = request.data.get("config_file")
        
        # 验证必要字段
        if not name:
            return Response({"err_msg": "name不能为空", "code": 400}, status=HTTP_400_BAD_REQUEST)
        if not model_file:
            return Response({"err_msg": "model_file不能为空", "code": 400}, status=HTTP_400_BAD_REQUEST)
            
        # 验证模型类型是否有效
        if model_type not in dict(Model.MODEL_TYPE_CHOICES):
            return Response({"err_msg": f"无效的模型类型: {model_type}", "code": 400}, status=HTTP_400_BAD_REQUEST)
            
        # 验证模型状态是否有效
        if model_status not in dict(Model.MODEL_STATUS_CHOICES):
            return Response({"err_msg": f"无效的模型状态: {model_status}", "code": 400}, status=HTTP_400_BAD_REQUEST)
        
        # 检查模型名称是否已存在
        if Model.objects.filter(name=name).exists():
            return Response({"err_msg": f"model {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        try:
            # 保存模型文件到models目录
            model_dir = os.path.join(settings.BASE_DIR, 'models')
            if not os.path.exists(model_dir):
                os.makedirs(model_dir)
                
            # 生成唯一的文件名
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            model_filename = f"{name}_{timestamp}"
            
            # 获取原始文件扩展名
            _, ext = os.path.splitext(model_file.name)
            model_filename = f"{model_filename}{ext}"
            
            # 构建目标文件路径
            model_filepath = os.path.join(model_dir, model_filename)
            
            # 保存文件
            with open(model_filepath, 'wb+') as destination:
                for chunk in model_file.chunks():
                    destination.write(chunk)
            
            # 创建模型实例
            model = Model(
                name=name,
                desc=desc,
                model_file=model_filename,  # 保存文件名而不是文件对象
                config_file=config_file,
                size=model_size,
                model_type=model_type,
                model_status=model_status,
                creater=request.user
            )
            model.save()
            
            # 返回成功响应
            serializer = ModelSerializer(model)
            return Response(serializer.data, status=HTTP_201_CREATED)
            
        except Exception as e:
            # 如果保存过程中出错，删除已上传的文件
            if 'model_filepath' in locals() and os.path.exists(model_filepath):
                os.remove(model_filepath)
            return Response({"err_msg": f"创建模型失败: {str(e)}", "code": 500}, status=HTTP_400_BAD_REQUEST)
        
    
    def retrieve(self, request, *args, **kwargs):
        model = self.get_object()
        serializer = self.get_serializer(model)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
        
        
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        # 获取分页参数
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            response.data['code'] = 200
            return response

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            "data": serializer.data,
            "code": 200
        })
    
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # 获取要更新的字段
        name = request.data.get('name')
        desc = request.data.get('desc')
        model_type = request.data.get('model_type')
        model_status = request.data.get('model_status')
        
        # 验证模型类型是否有效
        if model_type and model_type not in dict(Model.MODEL_TYPE_CHOICES):
            return Response({"err_msg": f"无效的模型类型: {model_type}", "code": 400}, status=HTTP_400_BAD_REQUEST)
            
        # 验证模型状态是否有效
        if model_status and model_status not in dict(Model.MODEL_STATUS_CHOICES):
            return Response({"err_msg": f"无效的模型状态: {model_status}", "code": 400}, status=HTTP_400_BAD_REQUEST)
        
        # 检查名称是否已存在（排除当前实例）
        if name and name != instance.name and Model.objects.filter(name=name).exists():
            return Response({"err_msg": f"model {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        try:
            # 更新字段
            if name:
                instance.name = name
            if desc:
                instance.desc = desc
            if model_type:
                instance.model_type = model_type
            if model_status:
                instance.model_status = model_status
                
            instance.save()
            
            serializer = ModelSerializer(instance)
            return Response(serializer.data)
            
        except Exception as e:
            return Response({"err_msg": f"更新模型失败: {str(e)}", "code": 500}, status=HTTP_400_BAD_REQUEST)
            
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        
        try:
            # 删除模型文件
            if instance.model_file:
                model_filepath = os.path.join(settings.BASE_DIR, 'models', instance.model_file)
                if os.path.exists(model_filepath):
                    os.remove(model_filepath)
            
            # 删除数据库记录
            instance.delete()
            
            return Response({"msg": "删除成功", "code": 200}, status=HTTP_204_NO_CONTENT)
            
        except Exception as e:
            return Response({"err_msg": f"删除模型失败: {str(e)}", "code": 500}, status=HTTP_400_BAD_REQUEST)
        
    
    @action(detail=True, methods=['get'])
    def compatible_datasets(self, request, pk=None):
        """
        获取与指定模型兼容的数据集列表
        """
        try:
            # 获取模型实例
            model = self.get_object()
            model_type = model.model_type

            # 查找模型类型对应的数据集类型
            dataset_category = None
            for category, model_types in MODEL_TO_DATASET_TYPE_MAP.items():
                if model_type in model_types:
                    dataset_category = category
                    break

            if not dataset_category:
                return Response({
                    "err_msg": f"未找到模型类型 {model_type} 对应的数据集类型",
                    "code": 400
                }, status=HTTP_400_BAD_REQUEST)

            # 获取当前用户
            user = request.user

            # 构建数据集查询
            queryset = Dataset.objects.filter(dataset_type=dataset_category)

            # 如果不是管理员，只显示自己的数据集
            if not user.is_staff:
                queryset = queryset.filter(creater=user.id)

            # 序列化数据
            serializer = DatasetBasicSerializer(queryset, many=True)

            return Response({
                "data": serializer.data,
                "code": 200,
                "msg": "获取成功"
            }, status=HTTP_200_OK)

        except Model.DoesNotExist:
            return Response({
                "err_msg": "模型不存在",
                "code": 404
            }, status=HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                "err_msg": f"获取数据集失败: {str(e)}",
                "code": 500
            }, status=HTTP_400_BAD_REQUEST)
            
            

class ModelDeductionView(GenericAPIView):
    
    """
    根据model id, 进行推演
    """

    # queryset = Model.objects.all()
    serializer_class = ModelDeductionSerializer
    
    def post(self, request):
        
        user = request.user
        model_id = request.data.get("model_id")
        entrypoint = request.data.get("entrypoint")
        print(f"entrypoint: {entrypoint}")
        # if not entrypoint:
        #     entrypoint = "python ray_eval_jsbsim3v1_record.py"
        
        if model_id is not None:
            model = Model.objects.filter(model_id=model_id).first()
            if model:
                # 进行推演
                timestamp = time.time()
                timestamp_str = str(timestamp)
                
                name = model.name + '-' + timestamp_str
                
                deduction = {
                    "name": name,
                    "creater": user.id,
                    "model": model.id,
                    "status": Deduction.StatusChoice.INITIALIZED
                }
                
                seria = self.get_serializer(data=deduction)
                if seria.is_valid():
                    deduction_inst = seria.save()
                    
                    create_inferrence(deduction_inst, entrypoint)
                    return Response({"code": 200, "data": seria.data, "msg": "deduction success."}, status=HTTP_200_OK)

        return Response({"code": 400, "err_msg": "please send correct task_id."}, status=HTTP_400_BAD_REQUEST)             
            
        
    
    
        
        
            
    
    