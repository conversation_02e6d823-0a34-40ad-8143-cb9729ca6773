# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Publish pip package to PyPI https://pypi.org/project/ultralytics/

name: Publish to Py<PERSON>

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      pypi:
        type: boolean
        description: Publish to PyPI

jobs:
  check:
    if: github.repository == 'ultralytics/ultralytics' && github.actor == 'glenn-jocher'
    runs-on: ubuntu-latest
    permissions:
      contents: write
    outputs:
      increment: ${{ steps.check_pypi.outputs.increment }}
      current_tag: ${{ steps.check_pypi.outputs.current_tag }}
      previous_tag: ${{ steps.check_pypi.outputs.previous_tag }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - uses: astral-sh/setup-uv@v5
      - run: uv pip install --system --no-cache ultralytics-actions
      - id: check_pypi
        shell: python
        run: |
          import os
          from actions.utils import check_pypi_version
          local_version, online_version, publish = check_pypi_version()
          os.system(f'echo "increment={publish}" >> $GITHUB_OUTPUT')
          os.system(f'echo "current_tag=v{local_version}" >> $GITHUB_OUTPUT')
          os.system(f'echo "previous_tag=v{online_version}" >> $GITHUB_OUTPUT')
          if publish:
              print('Ready to publish new version to PyPI ✅.')
      - name: Tag and Release
        if: steps.check_pypi.outputs.increment == 'True'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CURRENT_TAG: ${{ steps.check_pypi.outputs.current_tag }}
          PREVIOUS_TAG: ${{ steps.check_pypi.outputs.previous_tag }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          git config --global user.name "UltralyticsAssistant"
          git config --global user.email "<EMAIL>"
          git tag -a "$CURRENT_TAG" -m "$(git log -1 --pretty=%B)"
          git push origin "$CURRENT_TAG"
          ultralytics-actions-summarize-release
          uv cache prune --ci

  build:
    needs: check
    if: needs.check.outputs.increment == 'True'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - uses: astral-sh/setup-uv@v5
      - run: uv pip install --system --no-cache build
      - run: python -m build
      - uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/
      - run: uv cache prune --ci

  publish:
    needs: [check, build]
    if: needs.check.outputs.increment == 'True'
    runs-on: ubuntu-latest
    environment: # for GitHub Deployments tab
      name: Release - PyPI
      url: https://pypi.org/p/ultralytics
    permissions:
      id-token: write # for PyPI trusted publishing
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist/
      - uses: pypa/gh-action-pypi-publish@release/v1

  notify:
    needs: [check, publish]
    if: always() && needs.check.outputs.increment == 'True'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Extract PR Details
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          PR_JSON=$(gh pr list --search "${GITHUB_SHA}" --state merged --json number,title --jq '.[0]')
          PR_NUMBER=$(echo "${PR_JSON}" | jq -r '.number')
          PR_TITLE=$(echo "${PR_JSON}" | jq -r '.title')
          echo "PR_NUMBER=${PR_NUMBER}" >> "${GITHUB_ENV}"
          echo "PR_TITLE=${PR_TITLE}" >> "${GITHUB_ENV}"
      - name: Notify Success
        if: needs.publish.result == 'success' && github.event_name == 'push'
        uses: slackapi/slack-github-action@v2.0.0
        with:
          webhook-type: incoming-webhook
          webhook: ${{ secrets.SLACK_WEBHOOK_URL_YOLO }}
          payload: |
            text: "<!channel> GitHub Actions success for ${{ github.workflow }} ✅\n\n\n*Repository:* https://github.com/${{ github.repository }}\n*Action:* https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\n*Author:* ${{ github.actor }}\n*Event:* NEW `${{ github.repository }} ${{ needs.check.outputs.current_tag }}` pip package published 😃\n*Job Status:* ${{ job.status }}\n*Pull Request:* <https://github.com/${{ github.repository }}/pull/${{ env.PR_NUMBER }}> ${{ env.PR_TITLE }}\n"
      - name: Notify Failure
        if: needs.publish.result != 'success'
        uses: slackapi/slack-github-action@v2.0.0
        with:
          webhook-type: incoming-webhook
          webhook: ${{ secrets.SLACK_WEBHOOK_URL_YOLO }}
          payload: |
            text: "<!channel> GitHub Actions error for ${{ github.workflow }} ❌\n\n\n*Repository:* https://github.com/${{ github.repository }}\n*Action:* https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\n*Author:* ${{ github.actor }}\n*Event:* ${{ github.event_name }}\n*Job Status:* ${{ job.status }}\n*Pull Request:* <https://github.com/${{ github.repository }}/pull/${{ env.PR_NUMBER }}> ${{ env.PR_TITLE }}\n"
