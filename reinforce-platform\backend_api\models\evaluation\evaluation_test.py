import uuid
from django.db import models


class EvaluationTest(models.Model):
    """
    评估测试模型
    入库测试用以确定与评估关联的策略是否正确支持该评估代码。
    默认的入库测试是运行评估代码，看是否能不报错完成，类似于集成测试。
    每个评估可额外配置其他测试代码，如测试输出格式是否正确的单元测试。
    """
    
    class StatusChoice(models.IntegerChoices):
        """状态choice"""
        PENDING = 0, '待执行'
        RUNNING = 1, '执行中'
        PASSED = 2, '通过'
        FAILED = 3, '失败'
    
    test_id = models.UUIDField('测试ID', default=uuid.uuid4, editable=False)
    name = models.CharField('测试名称', max_length=255)
    desc = models.CharField('测试描述', max_length=1024, null=True)
    
    # 测试代码
    test_code = models.FileField('测试代码', upload_to='evaluations/tests', null=True)
    
    # 测试状态
    status = models.IntegerField('状态', choices=StatusChoice.choices, default=StatusChoice.PENDING)
    
    # 测试结果
    result = models.TextField('测试结果', null=True)
    log = models.TextField('测试日志', null=True)
    
    # 关联的评估
    evaluation = models.ForeignKey('backend_api.Evaluation', on_delete=models.CASCADE, 
                                  related_name='tests', verbose_name='关联评估')
    
    # 时间信息
    create_time = models.DateTimeField('创建时间', auto_now_add=True)
    start_time = models.DateTimeField('开始时间', null=True)
    end_time = models.DateTimeField('结束时间', null=True)
    
    # 是否为默认入库测试
    is_default = models.BooleanField('默认测试', default=False)
    
    class Meta:
        verbose_name = '评估测试'
        verbose_name_plural = verbose_name
        ordering = ('-id',) 