from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.parsers import MultiPartParser
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR
from django.db.models import Q
from datetime import datetime
from django.utils import timezone
from rest_framework.views import APIView
import logging
import psutil
import os
from django.conf import settings
from pathlib import Path

from backend_api.models.emulator import Emulator
from backend_api.serializers.emulator import EmulatorSerializer

logger = logging.getLogger(__name__)

class EmulatorViewSets(ModelViewSet):
    """
    声明仿真器资源类 用户操作:获取仿真信息  更新仿真  删除仿真 创建仿真
    """
    queryset = Emulator.objects.all()
    serializer_class = EmulatorSerializer
    parser_classes = [MultiPartParser]
    
    def get_queryset(self):
        user = self.request.user
        queryset = Emulator.objects.all()
        
        # 获取过滤参数
        name = self.request.query_params.get('name', '')
        creater_name = self.request.query_params.get('creater', '')
        start_time = self.request.query_params.get('start_date', '')
        end_time = self.request.query_params.get('end_date', '')
        engine_type = self.request.query_params.get('engine_type', '')
        need_render = self.request.query_params.get('need_render', None)
        ids = self.request.query_params.get('ids', '')
        
        # 按指定ID过滤
        if ids:
            try:
                # 支持单个ID或逗号分隔的多个ID
                id_list = [int(id_str.strip()) for id_str in ids.split(',') if id_str.strip()]
                if id_list:
                    queryset = queryset.filter(id__in=id_list)
            except ValueError:
                # 如果ID格式不正确，返回空查询集
                return queryset.none()
        
        # 按名称过滤
        if name:
            queryset = queryset.filter(name__icontains=name)
        
        # 按创建者过滤
        if creater_name:
            queryset = queryset.filter(creater__username__icontains=creater_name)
        
        # 按引擎类型过滤
        if engine_type:
            queryset = queryset.filter(engine_type=engine_type)
            
        # 按need_render过滤
        if need_render is not None:
            need_render_bool = str(need_render).lower() == 'true'
            queryset = queryset.filter(need_render=need_render_bool)
        
        # 按上传时间范围过滤 - 查找 开始时间 <= 创建时间 <= 结束时间 的数据
        if start_time and end_time:
            try:
                # 解析开始日期，设置为当天的开始时间 00:00:00
                start_datetime = datetime.strptime(start_time, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime)
                # 解析结束日期，设置为当天的结束时间 23:59:59
                end_datetime = datetime.strptime(end_time, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime)
                # 查找在时间范围内的数据
                queryset = queryset.filter(create_time__gte=start_datetime, create_time__lte=end_datetime)
            except ValueError:
                pass
        elif start_time:
            try:
                # 只有开始时间，查找大于等于开始时间的数据
                start_datetime = datetime.strptime(start_time, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime)
                queryset = queryset.filter(create_time__gte=start_datetime)
            except ValueError:
                pass
        elif end_time:
            try:
                # 只有结束时间，查找小于等于结束时间的数据
                end_datetime = datetime.strptime(end_time, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime)
                queryset = queryset.filter(create_time__lte=end_datetime)
            except ValueError:
                pass
        
        # 非管理员用户只能查看自己的仿真器
        if not user.is_staff:
            queryset = queryset.filter(creater=user.id)
            
        return queryset
    
    def create(self, request):
        user = request.user
        
        # 打印原始请求数据以进行调试
        print("Original request.data:", dict(request.data))
        print("Original request.FILES:", dict(request.FILES))
        
        # 创建一个新的字典来存储数据，而不是复制整个request.data
        request_data = {}
        
        # 从request.data中复制需要的字段
        fields_to_copy = ['name', 'desc', 'base_image', 'engine_type', 'engine_size', 'need_render']
        for field in fields_to_copy:
            value = request.data.get(field)
            print(f"Processing field {field}, value: {value}, type: {type(value)}")
            
            if value not in ['', None]:
                # 确保数值类型字段正确转换
                if field == 'engine_size' and value:
                    try:
                        request_data[field] = int(value)
                    except (ValueError, TypeError):
                        continue
                # 确保布尔类型字段正确转换
                elif field == 'need_render':
                    request_data[field] = str(value).lower() in ['true', '1', 'yes']
                # 其他字符串字段
                else:
                    request_data[field] = str(value)
            elif field == 'need_render':  # 为need_render设置默认值
                request_data[field] = False
                
        print("Processed request_data:", request_data)
        
        # 设置创建者
        request_data["creater"] = user.id
        
        # 检查名称是否已存在
        name = request_data.get("name")
        if name and Emulator.objects.filter(name__exact=name).exists():
                return Response({"err_msg": f"emulator {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        # 处理配置文件
        config_file = request.FILES.get('config_file')
        if config_file:
            # 设置文件存储路径
            file_path = os.path.join(settings.MEDIA_ROOT, 'emulators/configs', config_file.name)
            request_data["address"] = file_path
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存文件到指定位置
            with open(file_path, 'wb+') as destination:
                for chunk in config_file.chunks():
                    destination.write(chunk)
        
        seria = self.get_serializer(data=request_data)
        if seria.is_valid():
            emulator_inst = seria.save()
            return Response({"data": self.get_serializer(emulator_inst).data, "msg": "创建仿真器成功", "code": 200}, status=HTTP_201_CREATED)
        else:
            return Response({"err_msg": seria.errors, "msg": "参数错误", "code": 401}, status=HTTP_400_BAD_REQUEST)

class EmulatorStatsView(APIView):
    """仿真器状态统计视图"""
    
    def get(self, request):
        """获取仿真器状态统计信息"""
        try:
            # 获取仿真环境统计信息
            stats = {
                "emulators": self._get_emulator_stats(),
                "resources": self._get_resource_stats(),
                "storage": self._get_storage_stats()
            }
            
            return Response({
                "success": True,
                "data": stats
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get emulator stats: {e}", exc_info=True)
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"获取仿真器状态失败: {str(e)}"
                }
            }, status=HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _get_emulator_stats(self):
        """获取仿真环境统计"""
        from backend_api.models.rl_training import RLTrainingTask
        
        # 统计不同类型的仿真环境使用情况
        stats = {
            "total": 0,
            "running": 0,
            "types": {
                "内置仿真环境": {
                    "total": 0,
                    "running": 0
                },
                "外部仿真环境": {
                    "total": 0,
                    "running": 0
                }
            }
        }
        
        # 查询所有训练任务
        tasks = RLTrainingTask.objects.all()
        
        for task in tasks:
            env_type = "外部仿真环境" if task.use_external_env else "内置仿真环境"
            stats["total"] += 1
            stats["types"][env_type]["total"] += 1
            
            if task.status == "running":
                stats["running"] += 1
                stats["types"][env_type]["running"] += 1
        
        return stats
    
    def _get_resource_stats(self):
        """获取资源使用统计"""
        stats = {
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "logical_count": psutil.cpu_count(logical=True)
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "used": psutil.virtual_memory().used,
                "percent": psutil.virtual_memory().percent
            },
            "swap": {
                "total": psutil.swap_memory().total,
                "used": psutil.swap_memory().used,
                "free": psutil.swap_memory().free,
                "percent": psutil.swap_memory().percent
            }
        }
        
        return stats
    
    def _get_storage_stats(self):
        """获取存储使用统计"""
        try:
            media_root = settings.MEDIA_ROOT
            if not media_root:
                logger.warning("MEDIA_ROOT is not configured")
                media_root = os.path.join(os.path.dirname(settings.BASE_DIR), 'media')
            
            stats = {
                "system": self._get_disk_usage("/"),  # 系统盘
                "data": self._get_disk_usage(str(Path(media_root))),  # 数据目录
            }
            
            # 添加额外的存储目录统计
            extra_paths = {
                "models": os.path.join(settings.BASE_DIR, 'models'),
                "datasets": os.path.join(settings.BASE_DIR, 'datasets'),
                "checkpoints": os.path.join(settings.BASE_DIR, 'checkpoints')
            }
            
            for name, path in extra_paths.items():
                if os.path.exists(path):
                    stats[name] = self._get_disk_usage(str(Path(path)))
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}", exc_info=True)
            return {
                "system": self._get_empty_disk_stats(),
                "data": self._get_empty_disk_stats()
            }
    
    def _get_disk_usage(self, path):
        """获取指定路径的磁盘使用情况"""
        try:
            # 确保路径是字符串类型
            path_str = str(path)
            
            # 检查路径是否存在
            if not os.path.exists(path_str):
                logger.warning(f"Path does not exist: {path_str}")
                return self._get_empty_disk_stats()
            
            # 获取磁盘使用情况
            usage = psutil.disk_usage(path_str)
            return {
                "total": usage.total,
                "used": usage.used,
                "free": usage.free,
                "percent": usage.percent,
                "exists": True
            }
        except PermissionError:
            logger.warning(f"Permission denied when accessing path: {path}")
            return {**self._get_empty_disk_stats(), "error": "permission_denied"}
        except Exception as e:
            logger.warning(f"Failed to get disk usage for {path}: {e}")
            return {**self._get_empty_disk_stats(), "error": str(e)}
    
    def _get_empty_disk_stats(self):
        """返回空的磁盘统计信息"""
        return {
            "total": 0,
            "used": 0,
            "free": 0,
            "percent": 0,
            "exists": False
        }
    
