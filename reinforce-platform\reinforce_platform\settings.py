"""
Django settings for reinforce_platform project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import datetime
import os
from pathlib import Path
import sys
import environ


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 从环境变量文件中加载配置
env = environ.Env()
environ.Env.read_env(str(BASE_DIR / '.env'))

# 设置 STATIC_ROOT
STATIC_ROOT = os.path.join(BASE_DIR, 'static_files')

# 设置 MEDIA_ROOT
MEDIA_ROOT = BASE_DIR / "common"

# 设置媒体文件的URL
MEDIA_URL = '/common/'

# 设置数据集根目录
DATASET_ROOT = os.path.join(BASE_DIR, 'datasets')
os.makedirs(DATASET_ROOT, exist_ok=True)

# 设置模型文件目录
MODEL_ROOT = os.path.join(BASE_DIR, 'models')
os.makedirs(MODEL_ROOT, exist_ok=True)


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-b%8=+uv*j&kl9r=%6f3v_z*xm(*s*56*oay!4&eu0h!xe_spcy'

# SECURITY WARNING: don't run with debug turned on in production!
if env.bool('DJANGO_DEBUG', default=False):
    DEBUG = True
else:
    DEBUG = False

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # custom app
    'backend_api.apps.BackendApiConfig',
    'user.apps.UserConfig',
    
    # 第三方插件
    'rest_framework',
    'rest_framework_simplejwt', 
    'rest_framework_swagger',
    'drf_yasg',
    'corsheaders',
    'channels',
    'django_filters',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'reinforce_platform.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# channel setting
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [(env('DJANGO_REDIS_HOST'), env('DJANGO_REDIS_PORT'))],
        },
    },
}


WSGI_APPLICATION = 'reinforce_platform.wsgi.application'
# ASGI_APPLICATION = 'reinforce_platform.routing.application'


# CORS SETTING
# 允许跨域
CORS_ORIGIN_ALLOW_ALL = True
# 允许携带cookie
CORS_ALLOW_CREDENTIALS = True

# 指定允许请求源
CORS_ALLOWED_ORIGINS = ["*"]

# User Setting
AUTH_USER_MODEL = 'user.User'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('DJANGO_DB_NAME'),
        'USER': env('DJANGO_DB_USER'),
        'PASSWORD': env('DJANGO_DB_PASSWORD'),
        'HOST': env('DJANGO_DB_HOST'),
        'PORT': env('DJANGO_DB_PORT'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
        'MYSQL': {
            'driver': 'pymysql',
            'charset': 'utf8mb4',
        }
    }
}

# RESTFULL 
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    # 'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    # 'PAGE_SIZE': 10,  # 设置每页显示的对象数量
    'DEFAULT_PAGINATION_CLASS': 'utils.pagination.CustomPageNumberPagination',
    'PAGE_SIZE': 15,  # 设置默认的每页显示数量
}

SIMPLE_JWT = {
    # token有效时长(返回的 access 有效时长)
    'ACCESS_TOKEN_LIFETIME': datetime.timedelta(hours=24),
    # token刷新的有效时间(返回的 refresh 有效时长)
    'REFRESH_TOKEN_LIFETIME': datetime.timedelta(hours=24),
}


# Celery配置  
CELERY_BROKER_URL = env('DJANGO_REDIS_URL')  # 消息代理URL（使用Redis）  
CELERY_RESULT_BACKEND = env('DJANGO_REDIS_URL')  # 结果后端URL（使用Redis）  
# CELERY_BROKER_URL = 'redis://:secret@172.16.20.60:36379/3'  # 消息代理URL（使用Redis）  
# CELERY_RESULT_BACKEND = 'redis://:secret@172.16.20.60:36379/3'  # 结果后端URL（使用Redis）  
# CELERY_ACCEPT_CONTENT = ['application/json']  # 接受的内容类型  
# CELERY_TASK_SERIALIZER = 'json'  # 任务序列化器
# CELERY_RESULT_SERIALIZER = 'json'  # 结果序列化器



# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
