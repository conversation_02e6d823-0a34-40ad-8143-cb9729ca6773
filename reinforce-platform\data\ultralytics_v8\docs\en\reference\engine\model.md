---
description: Explore the base class for implementing YOLO models with unified APIs for training, validation, prediction, and more. Learn how to utilize different task types and model configurations.
keywords: YOLO model, Ultralytics, machine learning, deep learning, PyTorch model, training, validation, prediction, exporting, benchmarking, Ultralytics HUB, Triton Server
---

# Reference for `ultralytics/engine/model.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/model.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.engine.model.Model

<br><br>
