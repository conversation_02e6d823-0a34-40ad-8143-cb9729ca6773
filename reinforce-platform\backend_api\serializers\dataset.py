from rest_framework import serializers
from backend_api.models.dataset import Dataset


class DatasetBasicSerializer(serializers.ModelSerializer):
    """
    数据集基础序列化器，用于返回指定字段
    """
    class Meta:
        model = Dataset
        fields = ['id', 'name']


class DatasetSerializer(serializers.ModelSerializer):
    """
    数据集基本信息序列化器
    """
    creator_name = serializers.SerializerMethodField(read_only=True)    
    
    def get_creator_name(self, obj):
        if obj.creater:
            return obj.creater.username
        return None
    
    class Meta:
        model = Dataset
        fields = [
            'id', 'dataset_id', 'name', 'description', 'dataset_type',
            'address', 'original_file', 'size', 'sample_count', 'dataset_status',
            'creater', 'creator_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['dataset_id', 'created_at', 'updated_at']


class DatasetDetailSerializer(serializers.ModelSerializer):
    """
    数据集详细信息序列化器
    """
    dataset_type_display = serializers.CharField(source='get_dataset_type_display', read_only=True)
    dataset_status_display = serializers.CharField(source='get_dataset_status_display', read_only=True)
    creator_name = serializers.SerializerMethodField(read_only=True)
    
    def get_creator_name(self, obj):
        if obj.creater:
            return obj.creater.username
        return None
    
    class Meta:
        model = Dataset
        fields = [
            'id', 'dataset_id', 'name', 'description', 'dataset_type',
            'dataset_type_display', 'address', 'original_file', 'size', 'sample_count',
            'dataset_status', 'dataset_status_display', 'creater',
            'creator_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['dataset_id', 'created_at', 'updated_at'] 