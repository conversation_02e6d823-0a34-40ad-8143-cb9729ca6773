from rest_framework import serializers
from backend_api.models.deduction import Deduction

class DeductionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Deduction
        fields = "__all__"
        

class ModelDeductionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Deduction
        fields = ["id", "deduction_id", "name", "create_time", "creater", "status", "model", "file"]
        
        
class ModelDeductionDetailSerializer(serializers.ModelSerializer):
    
    def get_file(self, deduction):
        if deduction.file:
            return deduction.file.url
        else:
            return None
    
    class Meta:
        model = Deduction
        fields = ["id", "deduction_id", "name", "create_time", "creater", "status", "model", "file"]   


