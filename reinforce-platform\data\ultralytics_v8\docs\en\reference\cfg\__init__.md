---
description: Explore the methods for managing and validating YOLO configurations in the Ultralytics configuration module. Enhance your YOLO experience.
keywords: Ultralytics, YOLO, configuration, cfg2dict, get_cfg, check_cfg, save_dir, deprecation, merge_args, yolo, settings, explorer
---

# Reference for `ultralytics/cfg/__init__.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/cfg/\_\_init\_\_.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/cfg/__init__.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/cfg/__init__.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.cfg.cfg2dict

<br><br><hr><br>

## ::: ultralytics.cfg.get_cfg

<br><br><hr><br>

## ::: ultralytics.cfg.check_cfg

<br><br><hr><br>

## ::: ultralytics.cfg.get_save_dir

<br><br><hr><br>

## ::: ultralytics.cfg._handle_deprecation

<br><br><hr><br>

## ::: ultralytics.cfg.check_dict_alignment

<br><br><hr><br>

## ::: ultralytics.cfg.merge_equals_args

<br><br><hr><br>

## ::: ultralytics.cfg.handle_yolo_hub

<br><br><hr><br>

## ::: ultralytics.cfg.handle_yolo_settings

<br><br><hr><br>

## ::: ultralytics.cfg.handle_yolo_solutions

<br><br><hr><br>

## ::: ultralytics.cfg.parse_key_value_pair

<br><br><hr><br>

## ::: ultralytics.cfg.smart_value

<br><br><hr><br>

## ::: ultralytics.cfg.entrypoint

<br><br><hr><br>

## ::: ultralytics.cfg.copy_default_cfg

<br><br>
