import os
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.parsers import MultiPartParser
from rest_framework.filters import SearchFilter
from rest_framework.status import HTTP_201_CREATED, HTTP_400_BAD_REQUEST, HTTP_200_OK
from rest_framework.generics import GenericAPI<PERSON>iew

from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.http import HttpResponse

from backend_api.models.scenario import Scenario
from backend_api.serializers.scenario import ScenarioDetailSerializer, ScenarioSerializer, ScenarioCreateSerializer, ScenarioCheckSerializer
from utils import k8s_client


class ScenarioViewSets(ModelViewSet):
    """
    声明用户资源类 用户操作:获取想定信息  更新想定  删除想定 创建想定
    """
    queryset = Scenario.objects.all()
    parser_classes = [MultiPartParser]
    serializer_class = ScenarioSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['color']
    # filter_class = ScenarioFilter

    def get_queryset(self):
        user = self.request.user
        queryset = Scenario.objects.filter(is_deleted=False).all()
        if user.is_staff:
            name =  self.request.query_params.get('name', '')
            emulator_id = self.request.query_params.get('emulator_id', '')
            if name and name != '':
                queryset = queryset.filter(name__icontains=name).all()
            if emulator_id and emulator_id != '':
                queryset = queryset.filter(emulator__emulator_id=emulator_id).all()
        else:
            name =  self.request.query_params.get('name', '')
            emulator_id = self.request.query_params.get('emulator_id', '')
            query = Q(creater=user.id) | Q(creater__is_staff=True)
            if name and name != '':
                queryset = queryset.filter(query).filter(name__icontains=name).all()
            if emulator_id and emulator_id != '':
                print(emulator_id)
                queryset = queryset.filter(query).filter(emulator__emulator_id=emulator_id).all()

        return queryset

    def create(self, request):
        user = request.user
        request.data["creater"] = user.id
        name = request.data["name"]
        if Scenario.objects.filter(creater=user.id, name=name, is_deleted=False).count() > 0:
            return Response({"err_msg": f"scenario {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        seria = ScenarioCreateSerializer(data=request.data)
        if seria.is_valid():
            scen_inst = seria.save()
            return Response({"data": seria.data, "msg": "create scenario success.", "code": 200}, status=HTTP_201_CREATED)
        else:
            return Response({"err_msg": seria.errors, "msg": "参数错误", "code": 401}, status=HTTP_400_BAD_REQUEST)
        
    def retrieve(self, request, *args, **kwargs):
        
        scenerio = self.get_object()
        serializer = ScenarioDetailSerializer(scenerio)
        
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
    
    
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        
        instance.is_deleted = True
        instance.deleted_time = timezone.now()
        instance.save(update_fields=["is_deleted", "deleted_time"])
        instance.delete()
        return Response({"msg": "delete scenario success.", "code": 200}, status=HTTP_200_OK)
        
        
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        # 分页操作
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ScenarioDetailSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ScenarioDetailSerializer(queryset, many=True)
        return Response(serializer.data)



class ScenarioDownloadFileView(GenericAPIView):
    
    serializer_class = ScenarioSerializer
    
    """
    声明用户资源类 用户操作:下载想定文件
    """
    def get(self, request, *args, **kwargs):
        id = int(request.query_params.get('id', '0'))
        scenario = Scenario.objects.get(id=id)
        file_path = scenario.file.path
        file_name = scenario.file.name
        if not os.path.exists(file_path):
            return Response({"msg": "文件不存在", "code": 404}, status=404)

        with open(file_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type="application/octet-stream")
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            return response
        
        
class ScenarioCheckFileView(GenericAPIView):
    
    parser_classes = [MultiPartParser]
    serializer_class = ScenarioCheckSerializer
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            return Response({"msg": "文件格式正确", "data": serializer.data, "code": 200}, status=200)
        
        return Response({"msg": "文件格式错误", "data": serializer.errors, "code": 400}, status=400)