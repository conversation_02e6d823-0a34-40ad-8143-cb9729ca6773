# Generated by Django 4.2.7 on 2025-08-07 03:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('backend_api', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModelConversionLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(blank=True, choices=[('started', '开始转换'), ('processing', '转换中'), ('completed', '转换完成'), ('failed', '转换失败')], max_length=20, null=True, verbose_name='转换状态')),
                ('start_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('conversion_format', models.CharField(blank=True, default='om', max_length=10, null=True, verbose_name='转换格式')),
                ('conversion_device', models.CharField(blank=True, default='npu', max_length=50, null=True, verbose_name='转换设备')),
                ('output_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='输出路径')),
                ('file_size_mb', models.FloatField(blank=True, default=0.0, null=True, verbose_name='文件大小(MB)')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('log_content', models.TextField(blank=True, null=True, verbose_name='转换日志')),
            ],
            options={
                'verbose_name': '模型转换日志',
                'verbose_name_plural': '模型转换日志',
                'db_table': 'model_conversion_logs',
                'ordering': ['-start_time'],
            },
        ),
        migrations.RemoveField(
            model_name='modelexportlog',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='modelexportlog',
            name='training_model',
        ),
        migrations.RemoveIndex(
            model_name='trainingmodel',
            name='training_mo_task_id_db26c1_idx',
        ),
        migrations.RemoveIndex(
            model_name='trainingmodel',
            name='training_mo_created_deafe3_idx',
        ),
        migrations.RemoveIndex(
            model_name='trainingmodel',
            name='training_mo_export__355e4c_idx',
        ),
        migrations.RemoveIndex(
            model_name='trainingmodel',
            name='training_mo_created_a4866c_idx',
        ),
        migrations.RemoveField(
            model_name='trainingmodel',
            name='om_model_path',
        ),
        migrations.RemoveField(
            model_name='trainingmodel',
            name='task_id',
        ),
        migrations.RemoveField(
            model_name='trainingmodel',
            name='validation_error',
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='conversion_status',
            field=models.CharField(blank=True, default='pending', max_length=20, null=True, verbose_name='转换状态'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='converted_model_path',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='转换后的模型路径'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='epoch',
            field=models.IntegerField(default=0, verbose_name='训练轮次'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='is_best',
            field=models.BooleanField(default=False, verbose_name='是否为最佳模型'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='is_converted',
            field=models.BooleanField(default=False, verbose_name='模型格式转换'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='server_ip',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='服务器IP'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='server_password',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='服务器密码'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='server_port',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='服务器端口'),
        ),
        migrations.AddField(
            model_name='trainingmodel',
            name='task',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='models', to='backend_api.trainingtask'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='accuracy',
            field=models.FloatField(default=0.0, verbose_name='准确率'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='architecture',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='模型架构'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='fitness',
            field=models.FloatField(default=0.0, verbose_name='适应度'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='inference_speed',
            field=models.FloatField(default=0.0, verbose_name='推理速度(FPS)'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='inference_time_ms',
            field=models.FloatField(default=0.0, verbose_name='推理时间(毫秒)'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='model_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='模型名称'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='model_path',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='模型路径'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='model_size_mb',
            field=models.FloatField(default=0.0, verbose_name='模型大小(MB)'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='notes',
            field=models.TextField(blank=True, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='num_classes',
            field=models.IntegerField(default=0, verbose_name='类别数量'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='precision',
            field=models.FloatField(default=0.0, verbose_name='精度'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='recall',
            field=models.FloatField(default=0.0, verbose_name='召回率'),
        ),
        migrations.AlterField(
            model_name='trainingmodel',
            name='updated_time',
            field=models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='更新时间'),
        ),
        migrations.AlterModelTable(
            name='trainingmodel',
            table=None,
        ),
        migrations.DeleteModel(
            name='ModelExportLog',
        ),
        migrations.AddField(
            model_name='modelconversionlog',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作者'),
        ),
        migrations.AddField(
            model_name='modelconversionlog',
            name='training_model',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='conversion_logs', to='backend_api.trainingmodel', verbose_name='训练模型'),
        ),
    ]
