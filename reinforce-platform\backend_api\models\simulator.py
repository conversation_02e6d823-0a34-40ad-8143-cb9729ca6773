
import uuid
from django.db import models


class Simulator(models.Model):
    
    siumlator_id = models.UUIDField('仿真器id', default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield('仿真器名称', max_length=255, unique=True)
    desc = models.Char<PERSON>ield('仿真器描述', max_length=1024, null=True)
    address = models.CharField('仿真器地址', max_length=255, null=True)
    create_time = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    simulator_logo = models.FileField('仿真器logo', upload_to='siumlator/logos', null=True)
    config_file = models.FileField('仿真配置存储地址', upload_to='siumlator/configs', null=True)
    base_image = models.CharField('基础镜像', max_length=255, null=True)
    
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '仿真器实例'
        verbose_name_plural = verbose_name
        ordering = ('-id',)