from django.db import transaction
from backend_api.models.task import Task
from backend_api.models.training import Training
from backend_api.models.deduction import Deduction
from backend_api.models.evaluation.evaluation import Evaluation
from backend_api.models.policy.policy_evaluation import PolicyEvaluation


class TaskFactory:
    """任务工厂类，用于创建不同类型的任务"""

    @staticmethod
    @transaction.atomic
    def create_training_task(task_data, specific_data):
        """
        创建训练任务
        Args:
            task_data: 基本任务数据
            specific_data: 训练特定数据
        Returns:
            task: Task实例
            training: Training实例
        """
        # 创建基本任务
        task = Task.objects.create(
            name=task_data['name'],
            task_type=Task.TaskType.TRAINING,
            owner_id=task_data['owner'],
            status=task_data.get('status', Task.StatusChoice.INITIALIZED)
        )

        # 创建训练配置
        training = Training.objects.create(
            task=task,
            model_id=specific_data.get('model'),
            algorithm_id=specific_data.get('algorithm'),
            entrypoint=specific_data.get('entrypoint'),
            mount_path=specific_data.get('mount_path'),
            tb_url=specific_data.get('tb_url'),
            # 设置默认资源配置
            actor_num=specific_data.get('actor_num', 1),
            learner_num=specific_data.get('learner_num', 1),
            actor_per_cpu=specific_data.get('actor_per_cpu', 1.0),
            actor_per_gpu=specific_data.get('actor_per_gpu', 0.0),
            actor_per_memory=specific_data.get('actor_per_memory', 256),
            learner_per_cpu=specific_data.get('learner_per_cpu', 10.0),
            learner_per_gpu=specific_data.get('learner_per_gpu', 1.0),
            learner_per_memory=specific_data.get('learner_per_memory', 256)
        )

        return task, training

    @staticmethod
    @transaction.atomic
    def create_deduction_task(task_data, specific_data):
        """
        创建推演任务
        Args:
            task_data: 基本任务数据
            specific_data: 推演特定数据
        Returns:
            task: Task实例
            deduction: Deduction实例
        """
        # 创建基本任务
        task = Task.objects.create(
            name=task_data['name'],
            task_type=Task.TaskType.DEDUCTION,
            owner_id=task_data['owner'],
            status=task_data.get('status', Task.StatusChoice.INITIALIZED)
        )

        # 创建推演配置
        deduction = Deduction.objects.create(
            task=task,
            name=task_data['name'],
            model_id=specific_data.get('model'),
            creater_id=task_data['owner'],
            desc=specific_data.get('desc', ''),
            file=specific_data.get('mount_path'),  # 使用mount_path作为文件路径
            status=Deduction.StatusChoice.INITIALIZED
        )

        return task, deduction

    @staticmethod
    @transaction.atomic
    def create_evaluation_task(task_data, specific_data):
        """
        创建评估任务
        Args:
            task_data: 基本任务数据
            specific_data: 评估特定数据
        Returns:
            task: Task实例
            evaluation: Evaluation实例
        """
        # 创建基本任务
        task = Task.objects.create(
            name=task_data['name'],
            task_type=Task.TaskType.EVALUATION,
            owner_id=task_data['owner'],
            status=task_data.get('status', Task.StatusChoice.INITIALIZED)
        )

        # 获取评估配置
        evaluation_id = specific_data.get('evaluation')
        evaluation = Evaluation.objects.get(id=evaluation_id)

        # 创建评估记录
        policy_evaluation = PolicyEvaluation.objects.create(
            policy_id=specific_data.get('model'),  # 使用model_id作为policy_id
            evaluation=evaluation,
            status=PolicyEvaluation.StatusChoice.PENDING
        )

        return task, policy_evaluation

    @classmethod
    def create_task(cls, task_type, task_data, specific_data):
        """
        根据任务类型创建对应的任务
        Args:
            task_type: 任务类型
            task_data: 基本任务数据
            specific_data: 特定类型任务数据
        Returns:
            task: Task实例
            specific_task: 特定类型任务实例
        """
        if task_type == Task.TaskType.TRAINING:
            return cls.create_training_task(task_data, specific_data)
        elif task_type == Task.TaskType.DEDUCTION:
            return cls.create_deduction_task(task_data, specific_data)
        elif task_type == Task.TaskType.EVALUATION:
            return cls.create_evaluation_task(task_data, specific_data)
        else:
            raise ValueError(f"不支持的任务类型: {task_type}") 