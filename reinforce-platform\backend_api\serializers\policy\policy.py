from rest_framework import serializers
from backend_api.models.policy.policy import Policy


class PolicySerializer(serializers.ModelSerializer):
    """
    策略序列化器
    """
    class Meta:
        model = Policy
        fields = '__all__'


class PolicyDetailSerializer(serializers.ModelSerializer):
    """
    策略详情序列化器，包含更多关联信息
    """
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    policy_type_display = serializers.CharField(source='get_policy_type_display', read_only=True)
    creater_name = serializers.Char<PERSON>ield(source='creater.username', read_only=True)
    
    class Meta:
        model = Policy
        fields = [
            'id', 'policy_id', 'name', 'desc', 'code_file', 
            'status', 'status_display', 'policy_type', 'policy_type_display',
            'model', 'features', 'create_time', 'update_time', 
            'creater', 'creater_name'
        ] 