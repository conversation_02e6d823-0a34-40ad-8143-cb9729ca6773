# Generated by Django 4.2.7 on 2025-08-06 03:40

from django.db import migrations, models
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Algorithm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('algorithm_id', models.UUIDField(blank=True, default=uuid.uuid4, editable=False, null=True, verbose_name='算法id')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='算法名称')),
                ('artifact_name', models.CharField(max_length=255, unique=True, verbose_name='镜像名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='算法描述')),
                ('gitlab_url', models.URLField(blank=True, help_text='环境相关代码的GitLab仓库地址', max_length=1024, null=True, verbose_name='GitLab仓库地址')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '算法实例',
                'verbose_name_plural': '算法实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Dataset',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('dataset_id', models.CharField(editable=False, max_length=64, unique=True, verbose_name='数据集ID')),
                ('name', models.CharField(max_length=128, verbose_name='数据集名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='数据集描述')),
                ('dataset_type', models.CharField(choices=[('image', '图片'), ('text', '文本'), ('video', '视频'), ('other', '其他')], default='other', max_length=20, verbose_name='数据集类型')),
                ('file', models.FileField(blank=True, null=True, upload_to='datasets', verbose_name='数据集文件')),
                ('address', models.CharField(blank=True, max_length=255, null=True, verbose_name='数据集存储地址')),
                ('original_file', models.CharField(blank=True, max_length=255, null=True, verbose_name='原始文件路径')),
                ('size', models.CharField(blank=True, max_length=64, null=True, verbose_name='数据集大小')),
                ('sample_count', models.IntegerField(default=0, verbose_name='样本数量')),
                ('dataset_status', models.CharField(choices=[('enabled', '启用'), ('disabled', '禁用')], default='enabled', max_length=20, verbose_name='数据集状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '数据集',
                'verbose_name_plural': '数据集',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Deduction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deduction_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='推演id')),
                ('name', models.CharField(max_length=255, verbose_name='推演名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='推演描述')),
                ('file', models.CharField(max_length=255, null=True, verbose_name='推演存储地址')),
                ('ts', models.CharField(max_length=255, null=True, verbose_name='时间戳')),
                ('status', models.IntegerField(choices=[(0, '队列中'), (1, '初始化'), (2, '生成中'), (3, '成功'), (4, '失败')], default=0, null=True, verbose_name='推演状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('start_time', models.DateTimeField(null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(null=True, verbose_name='结束时间')),
            ],
            options={
                'verbose_name': '推演实例',
                'verbose_name_plural': '推演实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='DLTrainingConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('config_id', models.CharField(default=uuid.uuid4, max_length=100, unique=True, verbose_name='配置ID')),
                ('config_name', models.CharField(max_length=100, verbose_name='配置名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('algorithm_config', models.JSONField(verbose_name='算法配置')),
                ('training_config', models.JSONField(verbose_name='训练配置')),
                ('resources_config', models.JSONField(verbose_name='资源配置')),
                ('parameters_config', models.JSONField(verbose_name='参数配置')),
                ('other_params_config', models.JSONField(verbose_name='其他参数配置')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '深度学习训练配置',
                'verbose_name_plural': '深度学习训练配置',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='Emulator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emulator_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='仿真器id')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='仿真器名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='仿真器描述')),
                ('address', models.CharField(max_length=255, null=True, verbose_name='仿真器地址')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('config_file', models.FileField(null=True, upload_to='emulators/configs', verbose_name='仿真配置存储地址')),
                ('base_image', models.CharField(max_length=255, null=True, verbose_name='基础镜像')),
                ('engine_type', models.CharField(max_length=255, null=True, verbose_name='引擎类型')),
                ('engine_size', models.IntegerField(null=True, verbose_name='引擎大小')),
                ('need_render', models.BooleanField(default=False, verbose_name='是否需要渲染')),
            ],
            options={
                'verbose_name': '仿真器实例',
                'verbose_name_plural': '仿真器实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Environment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('env_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='环境ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='环境名称')),
                ('artifact_name', models.CharField(max_length=255, unique=True, verbose_name='镜像名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='环境描述')),
                ('version', models.CharField(max_length=50, verbose_name='版本号')),
                ('status', models.IntegerField(choices=[(0, '可用'), (1, '已弃用')], default=0, verbose_name='状态')),
                ('image_file', models.FileField(blank=True, null=True, upload_to='environments/images', verbose_name='环境镜像文件')),
                ('harbor_url', models.URLField(blank=True, help_text='Harbor中存储的镜像地址', max_length=1024, null=True, verbose_name='Harbor镜像地址')),
                ('gitlab_url', models.URLField(blank=True, help_text='环境相关代码的GitLab仓库地址', max_length=1024, null=True, verbose_name='GitLab仓库地址')),
                ('env_variables', models.JSONField(blank=True, help_text='环境变量配置，JSON格式', null=True, verbose_name='环境变量')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deprecated_time', models.DateTimeField(blank=True, null=True, verbose_name='弃用时间')),
            ],
            options={
                'verbose_name': '环境配置',
                'verbose_name_plural': '环境配置',
                'ordering': ('-update_time',),
            },
        ),
        migrations.CreateModel(
            name='Evaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('evaluation_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='评估ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='评估名称')),
                ('type', models.CharField(blank=True, default='', max_length=50, verbose_name='评估类型')),
                ('description', models.CharField(blank=True, max_length=1024, null=True, verbose_name='评估描述')),
                ('code_file', models.FileField(blank=True, null=True, upload_to='evaluations/code', verbose_name='评估代码')),
                ('status', models.IntegerField(choices=[(0, '生效中'), (1, '已弃用'), (2, '开发中')], default=2, verbose_name='状态')),
                ('environment', models.CharField(blank=True, max_length=1024, null=True, verbose_name='环境依赖')),
                ('simulation_engine', models.CharField(blank=True, max_length=255, null=True, verbose_name='仿真引擎')),
                ('policy_spec', models.TextField(blank=True, null=True, verbose_name='策略规范')),
                ('gitlab_project_id', models.IntegerField(blank=True, null=True, verbose_name='GitLab项目ID')),
                ('gitlab_project_url', models.URLField(blank=True, max_length=1024, null=True, verbose_name='GitLab项目URL')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '评估',
                'verbose_name_plural': '评估',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='EvaluationTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='测试ID')),
                ('name', models.CharField(max_length=255, verbose_name='测试名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='测试描述')),
                ('test_code', models.FileField(null=True, upload_to='evaluations/tests', verbose_name='测试代码')),
                ('status', models.IntegerField(choices=[(0, '待执行'), (1, '执行中'), (2, '通过'), (3, '失败')], default=0, verbose_name='状态')),
                ('result', models.TextField(null=True, verbose_name='测试结果')),
                ('log', models.TextField(null=True, verbose_name='测试日志')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('start_time', models.DateTimeField(null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(null=True, verbose_name='结束时间')),
                ('is_default', models.BooleanField(default=False, verbose_name='默认测试')),
            ],
            options={
                'verbose_name': '评估测试',
                'verbose_name_plural': '评估测试',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Model',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='模型id')),
                ('name', models.CharField(max_length=255, verbose_name='模型名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='模型描述')),
                ('model_file', models.CharField(max_length=1024, null=True, verbose_name='模型文件名')),
                ('config_file', models.CharField(max_length=1024, null=True, verbose_name='模型配置文件名')),
                ('size', models.CharField(max_length=255, null=True, verbose_name='模型大小')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('model_create_time', models.DateTimeField(null=True, verbose_name='模型创建时间')),
                ('model_type', models.CharField(choices=[('目标检测车', '目标检测车'), ('野战指挥车', '野战指挥车'), ('远程精式火箭炮', '远程精式火箭炮'), ('无人机', '无人机'), ('智能火控', '智能火控'), ('无人车', '无人车'), ('未分类', '未分类')], default='未分类', max_length=50, verbose_name='模型类型')),
                ('model_status', models.CharField(choices=[('训练中', '训练中'), ('启用', '启用'), ('禁用', '禁用'), ('完成', '完成'), ('中断', '中断'), ('未知', '未知')], default='未知', max_length=50, verbose_name='模型状态')),
            ],
            options={
                'verbose_name': '模型实例',
                'verbose_name_plural': '模型实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='ModelExportLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(blank=True, choices=[('started', '开始导出'), ('processing', '处理中'), ('completed', '导出完成'), ('failed', '导出失败')], max_length=20, null=True, verbose_name='导出状态')),
                ('start_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('export_format', models.CharField(blank=True, default='om', max_length=10, null=True, verbose_name='导出格式')),
                ('export_device', models.CharField(blank=True, default='npu', max_length=50, null=True, verbose_name='导出设备')),
                ('output_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='输出路径')),
                ('file_size_mb', models.FloatField(blank=True, default=0.0, null=True, verbose_name='文件大小(MB)')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('log_content', models.TextField(blank=True, null=True, verbose_name='导出日志')),
            ],
            options={
                'verbose_name': '模型导出日志',
                'verbose_name_plural': '模型导出日志',
                'db_table': 'model_export_logs',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='ModelInferenceLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('input_source', models.CharField(blank=True, help_text='图片路径或数据源', max_length=500, null=True, verbose_name='输入源')),
                ('inference_time_ms', models.FloatField(blank=True, null=True, verbose_name='推理时间(毫秒)')),
                ('confidence_threshold', models.FloatField(blank=True, default=0.5, null=True, verbose_name='置信度阈值')),
                ('detections_count', models.IntegerField(blank=True, default=0, null=True, verbose_name='检测数量')),
                ('result_data', models.JSONField(blank=True, default=dict, help_text='检测结果的JSON数据', null=True, verbose_name='推理结果')),
                ('output_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='结果输出路径')),
                ('created_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='推理时间')),
            ],
            options={
                'verbose_name': '模型推理日志',
                'verbose_name_plural': '模型推理日志',
                'db_table': 'model_inference_logs',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='Policy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('policy_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='策略ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='策略名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='策略描述')),
                ('code_file', models.FileField(null=True, upload_to='policies/code', verbose_name='策略代码')),
                ('status', models.IntegerField(choices=[(0, '待入库测试'), (1, '测试中'), (2, '可用'), (3, '测试失败'), (4, '已弃用')], default=0, verbose_name='状态')),
                ('policy_type', models.IntegerField(choices=[(0, '规则策略'), (1, '强化学习策略'), (2, '混合策略')], default=0, verbose_name='策略类型')),
                ('features', models.JSONField(blank=True, null=True, verbose_name='特征标签')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '策略',
                'verbose_name_plural': '策略',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='PolicyEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('policy_evaluation_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='评估记录ID')),
                ('status', models.IntegerField(choices=[(0, '待评估'), (1, '评估中'), (2, '评估完成'), (3, '评估失败')], default=0, verbose_name='状态')),
                ('score', models.FloatField(blank=True, null=True, verbose_name='评估分数')),
                ('result', models.JSONField(blank=True, null=True, verbose_name='评估结果详情')),
                ('log', models.TextField(blank=True, null=True, verbose_name='评估日志')),
                ('elo_rating', models.IntegerField(blank=True, default=1000, null=True, verbose_name='ELO评分')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('start_time', models.DateTimeField(null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(null=True, verbose_name='结束时间')),
                ('is_applied', models.BooleanField(default=False, verbose_name='已应用结果')),
            ],
            options={
                'verbose_name': '策略评估',
                'verbose_name_plural': '策略评估',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='RLResourceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cpu_utilization', models.FloatField(verbose_name='CPU利用率')),
                ('npu_utilization', models.FloatField(default=0.0, verbose_name='NPU利用率')),
                ('memory_utilization', models.FloatField(verbose_name='内存利用率')),
                ('network_io', models.FloatField(blank=True, null=True, verbose_name='网络IO')),
                ('disk_io', models.FloatField(blank=True, null=True, verbose_name='磁盘IO')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='时间戳')),
            ],
            options={
                'verbose_name': '强化学习资源指标',
                'verbose_name_plural': '强化学习资源指标',
                'ordering': ['timestamp'],
            },
        ),
        migrations.CreateModel(
            name='RLTrainingConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('config_id', models.CharField(default=uuid.uuid4, max_length=100, unique=True, verbose_name='配置ID')),
                ('config_name', models.CharField(max_length=100, verbose_name='配置名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('algorithm_config', models.JSONField(verbose_name='算法配置')),
                ('simulation_config', models.JSONField(verbose_name='仿真配置')),
                ('agent_config', models.JSONField(verbose_name='智能体配置')),
                ('hyper_params_config', models.JSONField(verbose_name='超参数配置')),
                ('cluster_config', models.JSONField(verbose_name='集群配置')),
                ('output_config', models.JSONField(verbose_name='输出配置')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '强化学习训练配置',
                'verbose_name_plural': '强化学习训练配置',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='RLTrainingMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('episode', models.IntegerField(verbose_name='回合数')),
                ('cumulative_reward', models.FloatField(verbose_name='累积奖励')),
                ('average_reward', models.FloatField(blank=True, null=True, verbose_name='平均奖励')),
                ('episode_length', models.IntegerField(blank=True, null=True, verbose_name='回合长度')),
                ('exploration_rate', models.FloatField(blank=True, null=True, verbose_name='探索率')),
                ('policy_loss', models.FloatField(blank=True, null=True, verbose_name='策略损失')),
                ('value_loss', models.FloatField(blank=True, null=True, verbose_name='价值损失')),
                ('entropy', models.FloatField(blank=True, null=True, verbose_name='熵')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='时间戳')),
            ],
            options={
                'verbose_name': '强化学习训练指标',
                'verbose_name_plural': '强化学习训练指标',
                'ordering': ['episode'],
            },
        ),
        migrations.CreateModel(
            name='RLTrainingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('training_id', models.CharField(default=uuid.uuid4, max_length=100, unique=True, verbose_name='训练ID')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('algorithm_name', models.CharField(default='PPO', max_length=50, verbose_name='算法名称')),
                ('algorithm_version', models.CharField(default='torch-1.8.1', max_length=50, verbose_name='算法版本')),
                ('rl_type', models.CharField(choices=[('PPO', 'PPO'), ('A3C', 'A3C'), ('SAC', 'SAC'), ('DDPG', 'DDPG')], default='PPO', max_length=20, verbose_name='强化学习类型')),
                ('current_episode', models.IntegerField(default=0, verbose_name='当前回合')),
                ('total_episodes', models.IntegerField(default=1000, verbose_name='总回合数')),
                ('estimated_duration', models.IntegerField(blank=True, null=True, verbose_name='预估时长(秒)')),
                ('data_source', models.CharField(default='内置仿真环境', max_length=100, verbose_name='数据源')),
                ('use_external_env', models.BooleanField(default=False, verbose_name='使用外部环境')),
                ('external_env_address', models.CharField(blank=True, max_length=255, null=True, verbose_name='外部环境地址')),
                ('sample_time', models.CharField(default='0.1', max_length=20, verbose_name='采样时间')),
                ('action_space', models.CharField(default='离散动作空间', max_length=50, verbose_name='动作空间')),
                ('observation_space', models.CharField(default='连续观测空间', max_length=50, verbose_name='观测空间')),
                ('reward_function', models.CharField(default='标准奖励函数', max_length=50, verbose_name='奖励函数')),
                ('learning_rate', models.CharField(default='1e-5', max_length=20, verbose_name='学习率')),
                ('epochs', models.IntegerField(default=5000, verbose_name='训练轮数')),
                ('batch_size', models.IntegerField(default=32, verbose_name='批次大小')),
                ('learning_rate_strategy', models.CharField(default='余弦衰减', max_length=50, verbose_name='学习率策略')),
                ('compute_type', models.CharField(default='fp32', max_length=20, verbose_name='计算类型')),
                ('validation_ratio', models.FloatField(default=0.2, verbose_name='验证比例')),
                ('cpu_count', models.CharField(default='4', max_length=20, verbose_name='CPU数量')),
                ('gpu_count', models.CharField(default='1', max_length=20, verbose_name='GPU数量')),
                ('save_path', models.CharField(default='/models/rl_training', max_length=255, verbose_name='保存路径')),
                ('save_name', models.CharField(default='my_rl_model', max_length=100, verbose_name='保存名称')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('started_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_time', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': '强化学习训练任务',
                'verbose_name_plural': '强化学习训练任务',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='Scenario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scenario_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='想定id')),
                ('name', models.CharField(max_length=255, verbose_name='想定名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='想定描述')),
                ('address', models.CharField(max_length=255, null=True, verbose_name='想定存储地址')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('file', models.FileField(null=True, upload_to='scenarios', verbose_name='想定存储地址')),
                ('config_file', models.FileField(null=True, upload_to='scenarios/configs', verbose_name='想定配置文件存储地址')),
                ('color', models.SmallIntegerField(choices=[(0, '白色'), (1, '黑色'), (2, '红色'), (3, '橙色'), (4, '黄色'), (5, '绿色'), (6, '蓝色'), (7, '靛色'), (8, '紫色')], default=0, null=True, verbose_name='想定颜色')),
                ('run_image', models.CharField(max_length=255, null=True, verbose_name='run镜像')),
                ('run_args', models.CharField(max_length=1024, null=True, verbose_name='run启动命令')),
                ('run_result_path', models.CharField(max_length=255, null=True, verbose_name='run结果路径')),
                ('infrl_training_config', models.JSONField(null=True, verbose_name='训练配置')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_time', models.DateTimeField(null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': '想定实例',
                'verbose_name_plural': '想定实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Simulator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('siumlator_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='仿真器id')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='仿真器名称')),
                ('desc', models.CharField(max_length=1024, null=True, verbose_name='仿真器描述')),
                ('address', models.CharField(max_length=255, null=True, verbose_name='仿真器地址')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('simulator_logo', models.FileField(null=True, upload_to='siumlator/logos', verbose_name='仿真器logo')),
                ('config_file', models.FileField(null=True, upload_to='siumlator/configs', verbose_name='仿真配置存储地址')),
                ('base_image', models.CharField(max_length=255, null=True, verbose_name='基础镜像')),
            ],
            options={
                'verbose_name': '仿真器实例',
                'verbose_name_plural': '仿真器实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='任务id')),
                ('name', models.CharField(max_length=255, verbose_name='任务名称')),
                ('task_type', models.CharField(choices=[('training', '训练'), ('evaluation', '评估'), ('deduction', '推演')], default='training', max_length=20, verbose_name='任务类型')),
                ('create_time', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('start_time', models.DateTimeField(null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(null=True, verbose_name='释放时间')),
                ('status', models.SmallIntegerField(choices=[(0, '队列中'), (1, '初始化'), (2, '运行'), (3, '暂停'), (4, '终止'), (5, '耗尽'), (6, '成功'), (7, '失败'), (8, '异常'), (9, '完成'), (10, '恢复'), (11, '暂停中...'), (12, '恢复中...'), (13, '终止中...')], default=0, null=True, verbose_name='状态')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_time', models.DateTimeField(null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': '任务实例',
                'verbose_name_plural': '任务实例',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Training',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('actor_num', models.IntegerField(blank=True, default=0, verbose_name='actor数量')),
                ('learner_num', models.IntegerField(blank=True, default=0, verbose_name='learner数量')),
                ('actor_per_cpu', models.FloatField(blank=True, default=0, verbose_name='单个actor cpu数量')),
                ('actor_per_gpu', models.FloatField(blank=True, default=0, verbose_name='单个actor gpu数量')),
                ('actor_per_memory', models.FloatField(blank=True, default=0, verbose_name='单个actor 内存数量')),
                ('learner_per_cpu', models.FloatField(blank=True, default=0, verbose_name='单个learner cpu数量')),
                ('learner_per_gpu', models.FloatField(blank=True, default=0, verbose_name='单个learner gpu数量')),
                ('learner_per_memory', models.FloatField(blank=True, default=0, verbose_name='单个learner 内存数量')),
                ('entrypoint', models.CharField(blank=True, max_length=255, null=True, verbose_name='入口函数')),
                ('mount_path', models.CharField(blank=True, max_length=255, null=True, verbose_name='挂载路径')),
                ('tb_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='Tensorboard地址')),
            ],
            options={
                'verbose_name': '训练配置',
                'verbose_name_plural': '训练配置',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='TrainingMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('epoch', models.IntegerField(verbose_name='轮数')),
                ('train_loss', models.FloatField(verbose_name='训练损失')),
                ('val_loss', models.FloatField(verbose_name='验证损失')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='记录时间')),
                ('cpu_usage', models.FloatField(verbose_name='CPU使用率')),
                ('gpu_usage', models.FloatField(verbose_name='GPU使用率')),
                ('npu_usage', models.FloatField(verbose_name='NPU使用率')),
                ('memory_usage', models.FloatField(verbose_name='内存使用率')),
            ],
            options={
                'verbose_name': '训练指标',
                'verbose_name_plural': '训练指标',
            },
        ),
        migrations.CreateModel(
            name='TrainingModel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='模型ID')),
                ('task_id', models.IntegerField(blank=True, help_text='关联的训练任务ID', null=True, verbose_name='训练任务ID')),
                ('model_name', models.CharField(blank=True, help_text='任务ID+模型文件名', max_length=255, null=True, verbose_name='模型名称')),
                ('model_path', models.CharField(blank=True, help_text='.pt文件的完整路径', max_length=500, null=True, verbose_name='模型文件路径')),
                ('accuracy', models.FloatField(blank=True, default=0.0, help_text='模型准确率 (mAP50)', null=True, verbose_name='准确率')),
                ('precision', models.FloatField(blank=True, default=0.0, help_text='模型精度', null=True, verbose_name='精度')),
                ('recall', models.FloatField(blank=True, default=0.0, help_text='模型召回率', null=True, verbose_name='召回率')),
                ('inference_speed', models.FloatField(blank=True, default=0.0, help_text='推理速度 (FPS)', null=True, verbose_name='推理速度')),
                ('inference_time_ms', models.FloatField(blank=True, default=0.0, help_text='单次推理时间 (毫秒)', null=True, verbose_name='推理时间')),
                ('model_size_mb', models.FloatField(blank=True, default=0.0, help_text='模型文件大小 (MB)', null=True, verbose_name='模型大小')),
                ('export_status', models.CharField(choices=[('pending', '待导出'), ('exporting', '导出中'), ('completed', '已完成'), ('failed', '导出失败')], default='pending', max_length=20, verbose_name='导出状态')),
                ('om_model_path', models.CharField(blank=True, help_text='转换后的.om文件路径', max_length=500, null=True, verbose_name='OM模型路径')),
                ('num_classes', models.IntegerField(blank=True, default=0, help_text='模型支持的类别数量', null=True, verbose_name='类别数量')),
                ('architecture', models.CharField(blank=True, help_text='如YOLOv8n, YOLOv8s等', max_length=100, null=True, verbose_name='模型架构')),
                ('fitness', models.FloatField(blank=True, default=0.0, help_text='综合评估分数', null=True, verbose_name='适应度分数')),
                ('created_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')),
                ('notes', models.TextField(blank=True, help_text='模型相关备注信息', null=True, verbose_name='备注')),
                ('validation_error', models.TextField(blank=True, help_text='模型验证时的错误信息', null=True, verbose_name='验证错误')),
            ],
            options={
                'verbose_name': '训练模型',
                'verbose_name_plural': '训练模型',
                'db_table': 'training_models',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='TrainingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('algorithm_version', models.CharField(blank=True, default='v8', max_length=50, null=True, verbose_name='算法版本')),
                ('model_path', models.CharField(max_length=255, verbose_name='模型路径')),
                ('dataset_id', models.IntegerField(verbose_name='数据集ID')),
                ('dataset_name', models.CharField(max_length=100, verbose_name='数据集名称')),
                ('validation_ratio', models.FloatField(default=0.2, verbose_name='验证集比例')),
                ('cpu_count', models.CharField(default='0', max_length=10, verbose_name='CPU数量')),
                ('npu_count', models.CharField(default='0', max_length=10, verbose_name='NPU数量')),
                ('storage_size', models.CharField(default='0', max_length=20, verbose_name='存储大小')),
                ('learning_rate', models.CharField(default='0.01', max_length=20, verbose_name='学习率')),
                ('epochs', models.CharField(default='100', max_length=10, verbose_name='训练轮数')),
                ('max_grad_norm', models.CharField(default='0', max_length=20, verbose_name='梯度范数')),
                ('max_samples', models.CharField(default='0', max_length=20, verbose_name='最大样本数')),
                ('grad_accumulation', models.CharField(default='1', max_length=20, verbose_name='梯度累计')),
                ('batch_size', models.CharField(default='16', max_length=20, verbose_name='批处理大小')),
                ('learning_rate_strategy', models.CharField(default='', max_length=50, verbose_name='学习率策略')),
                ('compute_type', models.CharField(default='float32', max_length=50, verbose_name='计算类型')),
                ('optimizer', models.CharField(default='SGD', max_length=50, verbose_name='优化器')),
                ('momentum', models.CharField(default='0.937', max_length=20, verbose_name='动量')),
                ('weight_decay', models.CharField(default='0.0005', max_length=20, verbose_name='权重衰减')),
                ('epsilon', models.CharField(default='1e-8', max_length=20, verbose_name='epsilon')),
                ('dropout', models.CharField(default='0', max_length=20, verbose_name='dropout')),
                ('label_smoothing', models.CharField(default='0', max_length=20, verbose_name='标签平滑')),
                ('use_gradient_clipping', models.BooleanField(default=False, verbose_name='使用梯度裁剪')),
                ('use_mixed_precision', models.BooleanField(default=False, verbose_name='使用混合精度')),
                ('early_stopping', models.IntegerField(default=0, verbose_name='早停轮数')),
                ('checkpoint_freq', models.IntegerField(default=0, verbose_name='检查点频率')),
                ('warmup_steps', models.IntegerField(default=0, verbose_name='预热步数')),
                ('log_freq', models.IntegerField(default=100, verbose_name='日志频率')),
                ('activation', models.CharField(default='', max_length=50, verbose_name='激活函数')),
                ('initialization', models.CharField(default='', max_length=50, verbose_name='初始化方法')),
                ('normalization', models.CharField(default='', max_length=50, verbose_name='归一化方法')),
                ('attention_heads', models.IntegerField(default=0, verbose_name='注意力头数')),
            ],
            options={
                'verbose_name': '训练任务',
                'verbose_name_plural': '训练任务',
            },
        ),
    ]
