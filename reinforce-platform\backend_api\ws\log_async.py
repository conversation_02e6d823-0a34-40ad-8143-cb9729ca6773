import asyncio
import logging
import sys
from channels.generic.websocket import AsyncWeb<PERSON>cketConsumer
import json
from django.http import QueryDict
from utils import k8s_client


logger = logging.getLogger(__name__)

class LogConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        query_string = self.scope.get("query_string")
        log_args = QueryDict(query_string=query_string, encoding="utf-8")
        self.container_name = log_args.get("container_name")
        self.pod_name = log_args.get("pod_name")

        self.room_group_name = "chat" + self.pod_name
        print(
            f"connect to group {self.room_group_name}  channel name: {self.channel_name}",
            file=sys.stderr,
        )

        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()
        # await self.send_logs()
        self.log_task = asyncio.create_task(self.send_logs())

    async def disconnect(self, close_code):
        if hasattr(self, 'log_task'):
            self.log_task.cancel()
            
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json["message"]

        await self.channel_layer.group_send(
            self.room_group_name, {"type": "chat.message", "message": message}
        )
        pass

    async def chat_message(self, event):
        try:
            message = event["message"]
            message = message.decode("utf-8")
            # print(f"log_message: {message}", file=sys.stderr)
            await self.send(text_data=json.dumps({"message": message}))
        except Exception as e:
            print(f"Error in log_message: {e}", file=sys.stderr)

    async def send_logs(self):
        def stream_logs():
            try:
                # 这是同步函数，获取Pod日志流
                logs_stream = k8s_client.get_pod_logs(
                    namespace="rl-platform",
                    pod_name=self.pod_name,
                    container_name=self.container_name,
                )
                # 返回日志流迭代器
                return logs_stream
            except Exception as e:
                print(f"Error: {e}", file=sys.stderr)
                return None, str(e)

        # 使用 asyncio.to_thread 将同步操作放入线程中运行
        # logs_stream = await asyncio.to_thread(stream_logs)
        logs_stream = stream_logs()

        # 如果没有获取到日志流，则处理错误
        if logs_stream is None:
            error_message = f"Error retrieving logs for pod: {self.pod_name}"
            await self.channel_layer.group_send(
                self.room_group_name,
                {"type": "chat.message", "message": error_message},
            )
            return

        # 如果日志流存在，异步发送日志到 WebSocket 组
        try:
            for log in logs_stream:
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {"type": "chat.message", "message": log},
                )

        except Exception as e:
            print(f"Error while streaming logs: {e}", file=sys.stderr)
            error_message = f"Error streaming logs for pod: {self.pod_name}: {str(e)}"
            await self.channel_layer.group_send(
                self.room_group_name,
                {"type": "chat.message", "message": error_message},
            )
