import logging
import sys
from channels.generic.websocket import Web<PERSON><PERSON><PERSON>onsumer
import json
from asgiref.sync import async_to_sync
from django.http import QueryDict
from utils import k8s_client


logger = logging.getLogger(__name__)


class LogConsumer(WebsocketConsumer):
    def connect(self):
        query_string = self.scope.get("query_string")
        log_args = QueryDict(query_string=query_string, encoding="utf-8")
        self.container_name = log_args.get("container_name")
        self.pod_name = log_args.get("pod_name")

        self.room_group_name = "chat" + self.pod_name
        print(
            f"connect to group {self.room_group_name}  channel name: {self.channel_name}",
            file=sys.stderr,
        )

        async_to_sync(self.channel_layer.group_add)(
            self.room_group_name, self.channel_name
        )
        # self.channel_layer.group_add(self.room_group_name, self.channel_name)
        self.logs_stream = None
        
        self.accept()
        self.send_logs()

    def disconnect(self, close_code):
        async_to_sync(self.channel_layer.group_discard)(
            self.room_group_name, self.channel_name
        )
        # self.channel_layer.group_discard(self.room_group_name, self.channel_name)
        if self.logs_stream:
            self.logs_stream.close()

    def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json["message"]

        self.channel_layer.group_send(
            self.room_group_name, {"type": "chat.message", "message": message}
        )
        pass

    def chat_message(self, event):
        try:
            message = event["message"]
            # print(f"log_message: {message}", file=sys.stderr)
            self.send(text_data=json.dumps({"message": message}))
        except Exception as e:
            print(f"Error in log_message: {e}", file=sys.stderr)


    def send_logs(self):
        def stream_logs():
            try:
                # 这是同步函数，获取Pod日志流
                logs_stream = k8s_client.get_pod_logs(
                    namespace="rl-platform",
                    pod_name=self.pod_name,
                    container_name=self.container_name,
                )
                # 返回日志流迭代器
                return logs_stream
            except Exception as e:
                print(f"Error: {e}", file=sys.stderr)
                return None, str(e)

        # 使用 asyncio.to_thread 将同步操作放入线程中运行
        logs_stream = stream_logs()

        # 如果没有获取到日志流，则处理错误
        if logs_stream is None:
            error_message = f"Error retrieving logs for pod: {self.pod_name}"
            async_to_sync(self.channel_layer.group_send)(
                self.room_group_name,
                {"type": "chat.message", "message": error_message},
            )
            # self.channel_layer.group_send(
            #     self.room_group_name,
            #     {"type": "chat.message", "message": error_message},
            # )
            return

        # 如果日志流存在，异步发送日志到 WebSocket 组
        try:
            for log_line in logs_stream:
                log = log_line.decode("utf-8")
                print(
                    "group room send: ",
                    self.room_group_name,
                    " message: ",
                    log,
                    file=sys.stderr,
                )
                async_to_sync(self.channel_layer.group_send)(
                    self.room_group_name, {"type": "chat.message", "message": log}
                )
                # self.channel_layer.group_send(
                #     self.room_group_name,
                #     {"type": "chat.message", "message": log},
                # )

        except Exception as e:
            print(f"Error while streaming logs: {e}", file=sys.stderr)
            error_message = f"Error streaming logs for pod: {self.pod_name}: {str(e)}"
            async_to_sync(self.channel_layer.group_send)(
                self.room_group_name,
                {"type": "chat.message", "message": error_message},
            )
            # self.channel_layer.group_send(
            #     self.room_group_name,
            #     {"type": "chat.message", "message": error_message},
            # )
