import uuid
from django.db import models


class PolicyEvaluation(models.Model):
    """
    策略评估关联模型
    记录策略与评估的关联关系及评估结果
    策略可通过高级评估机制（如 ELO Rating），获得策略特定的分数与特征标签。
    """
    
    class StatusChoice(models.IntegerChoices):
        """状态choice"""
        PENDING = 0, '待评估'
        RUNNING = 1, '评估中'
        COMPLETED = 2, '评估完成'
        FAILED = 3, '评估失败'
    
    policy_evaluation_id = models.UUIDField('评估记录ID', default=uuid.uuid4, editable=False)
    
    # 关联策略和评估
    policy = models.ForeignKey('backend_api.Policy', on_delete=models.CASCADE, verbose_name='策略')
    evaluation = models.ForeignKey('backend_api.Evaluation', on_delete=models.CASCADE, verbose_name='评估')
    
    # 评估状态
    status = models.IntegerField('状态', choices=StatusChoice.choices, default=StatusChoice.PENDING)
    
    # 评估结果
    score = models.FloatField('评估分数', null=True, blank=True)
    result = models.JSONField('评估结果详情', null=True, blank=True)
    log = models.TextField('评估日志', null=True, blank=True)
    
    # ELO评分
    elo_rating = models.IntegerField('ELO评分', default=1000, null=True, blank=True)
    
    # 时间信息
    create_time = models.DateTimeField('创建时间', auto_now_add=True)
    start_time = models.DateTimeField('开始时间', null=True)
    end_time = models.DateTimeField('结束时间', null=True)
    
    # 评估结果是否应用到策略特征标签
    is_applied = models.BooleanField('已应用结果', default=False)
    
    class Meta:
        verbose_name = '策略评估'
        verbose_name_plural = verbose_name
        ordering = ('-id',)
        unique_together = ('policy', 'evaluation') 