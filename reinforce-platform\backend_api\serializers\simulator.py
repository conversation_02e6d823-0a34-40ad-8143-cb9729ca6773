from rest_framework import serializers
from backend_api.models.simulator import Simulator

class SimulatorSerializer(serializers.ModelSerializer):
    simulator_logo = serializers.SerializerMethodField()

    class Meta:
        model = Simulator
        fields = "__all__"

    def get_simulator_logo(self, simulator):
        if simulator.simulator_logo:
            return simulator.simulator_logo.name
        else:
            return None
