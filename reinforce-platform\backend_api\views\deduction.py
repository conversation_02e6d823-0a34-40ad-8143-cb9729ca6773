from rest_framework.response import Response
from rest_framework.parsers import MultiPart<PERSON>arser
from rest_framework.viewsets import ModelViewSet
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend

from backend_api.models.deduction import Deduction
from backend_api.serializers.deduction import DeductionSerializer
from utils import k8s_client


class DeductionViewSets(ModelViewSet):
    """
    声明用户资源类 用户操作:获取算法信息  更新算法  删除算法 创建算法
    """
    queryset = Deduction.objects.all()
    serializer_class = DeductionSerializer
    parser_classes = [MultiPartParser]
    
    def get_queryset(self):
        user = self.request.user
        queryset = self.queryset
        if user.is_staff:
            name =  self.request.query_params.get('name', '')
            if name and name != '':
                queryset = queryset.filter(name__icontains=name).all()
            model_id =  self.request.query_params.get('model_id', '')
            if model_id and model_id != '':
                queryset = queryset.filter(model__model_id=model_id).all()  
        else:
            name =  self.request.query_params.get('name', '')
            if name and name != '':
                queryset = queryset.filter(creater=user.id).filter(name__icontains=name).all()
            
            model_id =  self.request.query_params.get('model_id', '')
            # print(model_id)
            if model_id and model_id != '':
                queryset = queryset.filter(model__model_id=model_id).all()  
            
        return queryset
    
    def create(self, request):
        user = request.user
        request.data["creater"] = user.id
        name = request.data["name"]
        if Deduction.objects.filter(name=name).count() > 0:
            return Response({"err_msg": f"fight {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        seria = self.get_serializer(data=request.data)
        if seria.is_valid():
            algo_inst = seria.save()
            
            return Response({"data": self.get_serializer(algo_inst).data, "msg": "create algorithm success.", "code": 200}, status=HTTP_201_CREATED)
        else:
            return Response({"err_msg": seria.errors, "msg": "参数错误", "code": 401}, status=HTTP_400_BAD_REQUEST)
        
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        
        # 删除 k8s job
        # if instance.deduction_id:
        #     deduction_job_name = f"deduction-{instance.deduction_id}"
        #     k8s_client.delete_job("rl-platform",deduction_job_name)
        
        return Response({"msg": "delete algorithm success.", "code": 200}, status=HTTP_200_OK)
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        # 分页操作
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = DeductionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = DeductionSerializer(queryset, many=True)
        return Response(serializer.data)
    
