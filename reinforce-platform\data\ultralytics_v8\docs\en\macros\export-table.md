{%set tip1 = ':material-information-outline:{ title="conf, iou, agnostic_nms are also available when nms=True" }' %}
{%set tip2 = ':material-information-outline:{ title="conf, iou are also available when nms=True" }' %}
{%set tip3 = ':material-information-outline:{ title="imx format only supported for YOLOv8n model currently" }' %}

| Format                                             | `format` Argument | Model                                           | Metadata | Arguments                                                                                     |
| -------------------------------------------------- | ----------------- | ----------------------------------------------- | -------- | --------------------------------------------------------------------------------------------- |
| [PyTorch](https://pytorch.org/)                    | -                 | `{{ model_name or "yolo11n" }}.pt`              | ✅       | -                                                                                             |
| [TorchScript](../integrations/torchscript.md)      | `torchscript`     | `{{ model_name or "yolo11n" }}.torchscript`     | ✅       | `imgsz`, `optimize`, `nms`{{ tip1 }}, `batch`                                                 |
| [ONNX](../integrations/onnx.md)                    | `onnx`            | `{{ model_name or "yolo11n" }}.onnx`            | ✅       | `imgsz`, `half`, `dynamic`, `simplify`, `opset`, `nms`{{ tip1 }}, `batch`                     |
| [OpenVINO](../integrations/openvino.md)            | `openvino`        | `{{ model_name or "yolo11n" }}_openvino_model/` | ✅       | `imgsz`, `half`, `dynamic`, `int8`, `nms`{{ tip1 }}, `batch`, `data`                          |
| [TensorRT](../integrations/tensorrt.md)            | `engine`          | `{{ model_name or "yolo11n" }}.engine`          | ✅       | `imgsz`, `half`, `dynamic`, `simplify`, `workspace`, `int8`, `nms`{{ tip1 }}, `batch`, `data` |
| [CoreML](../integrations/coreml.md)                | `coreml`          | `{{ model_name or "yolo11n" }}.mlpackage`       | ✅       | `imgsz`, `half`, `int8`, `nms`{{ tip2 }}, `batch`                                             |
| [TF SavedModel](../integrations/tf-savedmodel.md)  | `saved_model`     | `{{ model_name or "yolo11n" }}_saved_model/`    | ✅       | `imgsz`, `keras`, `int8`, `nms`{{ tip1 }}, `batch`                                            |
| [TF GraphDef](../integrations/tf-graphdef.md)      | `pb`              | `{{ model_name or "yolo11n" }}.pb`              | ❌       | `imgsz`, `batch`                                                                              |
| [TF Lite](../integrations/tflite.md)               | `tflite`          | `{{ model_name or "yolo11n" }}.tflite`          | ✅       | `imgsz`, `half`, `int8`, `nms`{{ tip1 }}, `batch`, `data`                                     |
| [TF Edge TPU](../integrations/edge-tpu.md)         | `edgetpu`         | `{{ model_name or "yolo11n" }}_edgetpu.tflite`  | ✅       | `imgsz`                                                                                       |
| [TF.js](../integrations/tfjs.md)                   | `tfjs`            | `{{ model_name or "yolo11n" }}_web_model/`      | ✅       | `imgsz`, `half`, `int8`, `nms`{{ tip1 }}, `batch`                                             |
| [PaddlePaddle](../integrations/paddlepaddle.md)    | `paddle`          | `{{ model_name or "yolo11n" }}_paddle_model/`   | ✅       | `imgsz`, `batch`                                                                              |
| [MNN](../integrations/mnn.md)                      | `mnn`             | `{{ model_name or "yolo11n" }}.mnn`             | ✅       | `imgsz`, `batch`, `int8`, `half`                                                              |
| [NCNN](../integrations/ncnn.md)                    | `ncnn`            | `{{ model_name or "yolo11n" }}_ncnn_model/`     | ✅       | `imgsz`, `half`, `batch`                                                                      |
| [IMX500](../integrations/sony-imx500.md){{ tip3 }} | `imx`             | `{{ model_name or "yolov8n" }}_imx_model/`      | ✅       | `imgsz`, `int8`, `data`                                                                       |
| [RKNN](../integrations/rockchip-rknn.md)           | `rknn`            | `{{ model_name or "yolo11n" }}_rknn_model/`     | ✅       | `imgsz`, `batch`, `name`                                                                      |
