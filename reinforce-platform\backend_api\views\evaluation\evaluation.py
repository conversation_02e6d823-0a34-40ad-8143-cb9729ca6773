from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.viewsets import ModelViewSet
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend
from django.conf import settings

from backend_api.models.evaluation.evaluation import Evaluation
from backend_api.models.evaluation.evaluation_test import EvaluationTest
from backend_api.serializers.evaluation.evaluation import EvaluationSerializer, EvaluationDetailSerializer
from utils.gitlab_api import GitlabAPI


class EvaluationViewSets(ModelViewSet):
    """
    评估视图集，提供评估管理的API接口
    包括：获取评估信息、创建评估、更新评估、删除评估等
    """
    queryset = Evaluation.objects.all()
    serializer_class = EvaluationSerializer
    parser_classes = [JSONParser, MultiPartParser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status', 'type']
    
    def get_serializer_class(self):
        """根据操作类型选择不同的序列化器"""
        if self.action == 'list' or self.action == 'retrieve':
            return EvaluationDetailSerializer
        return EvaluationSerializer
    
    def get_queryset(self):
        """获取查询集，可根据用户权限过滤"""
        user = self.request.user
        queryset = self.queryset
        
        # 根据名称过滤
        name = self.request.query_params.get('name', '')
        if name:
            queryset = queryset.filter(name__icontains=name)
            
        # 根据类型过滤
        type_filter = self.request.query_params.get('type', '')
        if type_filter:
            queryset = queryset.filter(type=type_filter)
        
        # 如果不是管理员，只能看到自己创建的评估
        if not user.is_staff:
            queryset = queryset.filter(creater=user.id)
            
        return queryset
    
    def create(self, request):
        """创建评估"""
        user = request.user
        request.data["creater"] = user.id
        
        # 检查评估名称是否已存在
        name = request.data.get("name")
        if Evaluation.objects.filter(name=name).exists():
            return Response(
                {"err_msg": f"评估 {name} 已存在", "msg": "对象已存在", "code": "object_exists"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            evaluation = serializer.save()
            
            # 创建 GitLab 项目
            try:
                gitlab_api = GitlabAPI()
                project = gitlab_api.create_project(
                    name=f"evaluation-{evaluation.evaluation_id}",
                    description=evaluation.description or '',
                    namespace_id=getattr(settings, 'GITLAB_GROUP_ID', None)
                )
                
                # 更新评估对象的 GitLab 信息
                evaluation.gitlab_project_id = project['id']
                evaluation.gitlab_project_url = project['web_url']
                evaluation.save()
            except Exception as e:
                # GitLab 项目创建失败，删除评估对象
                evaluation.delete()
                return Response(
                    {"err_msg": f"GitLab项目创建失败: {str(e)}", "msg": "创建失败", "code": 500},
                    status=HTTP_400_BAD_REQUEST
                )
            
            # 创建默认的入库测试
            EvaluationTest.objects.create(
                name=f"{name}默认入库测试",
                desc="默认入库测试，验证策略是否能正确运行该评估",
                evaluation=evaluation,
                is_default=True
            )
            
            return Response(
                {"data": EvaluationDetailSerializer(evaluation).data, "msg": "评估创建成功", "code": 200}, 
                status=HTTP_201_CREATED
            )
        else:
            return Response(
                {"err_msg": serializer.errors, "msg": "参数错误", "code": 400}, 
                status=HTTP_400_BAD_REQUEST
            )
    
    def destroy(self, request, *args, **kwargs):
        """删除评估"""
        instance = self.get_object()
        
        # 如果存在 GitLab 项目，尝试删除
        if instance.gitlab_project_id:
            try:
                gitlab_api = GitlabAPI()
                gitlab_api.delete_project(instance.gitlab_project_id)
            except Exception as e:
                return Response(
                    {"err_msg": f"GitLab项目删除失败: {str(e)}", "msg": "删除失败", "code": 500},
                    status=HTTP_400_BAD_REQUEST
                )
        
        self.perform_destroy(instance)
        return Response({"msg": "评估删除成功", "code": 200}, status=HTTP_204_NO_CONTENT)
    
    def retrieve(self, request, *args, **kwargs):
        """获取单个评估详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
    
    def list(self, request, *args, **kwargs):
        """获取评估列表"""
        queryset = self.filter_queryset(self.get_queryset())
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK) 