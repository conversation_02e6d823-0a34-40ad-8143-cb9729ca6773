# Generated by Django 4.2.7 on 2025-08-06 03:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('backend_api', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='trainingmodel',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='trainingmetrics',
            name='task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='backend_api.trainingtask'),
        ),
        migrations.AddField(
            model_name='training',
            name='algorithm',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trainings', to='backend_api.algorithm', verbose_name='训练算法'),
        ),
        migrations.AddField(
            model_name='training',
            name='model',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trainings', to='backend_api.model', verbose_name='模型'),
        ),
        migrations.AddField(
            model_name='training',
            name='task',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='training', to='backend_api.task', verbose_name='关联任务'),
        ),
        migrations.AddField(
            model_name='task',
            name='owner',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='simulator',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='scenario',
            name='simulator',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='backend_api.simulator', verbose_name='想定对应的仿真器'),
        ),
        migrations.AddField(
            model_name='rltrainingtask',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='rltrainingmetrics',
            name='task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='backend_api.rltrainingtask'),
        ),
        migrations.AddField(
            model_name='rltrainingconfig',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='rlresourcemetrics',
            name='task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_metrics', to='backend_api.rltrainingtask'),
        ),
        migrations.AddField(
            model_name='policyevaluation',
            name='evaluation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='backend_api.evaluation', verbose_name='评估'),
        ),
        migrations.AddField(
            model_name='policyevaluation',
            name='policy',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='backend_api.policy', verbose_name='策略'),
        ),
        migrations.AddField(
            model_name='policy',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='policy',
            name='evaluations',
            field=models.ManyToManyField(related_name='policies', through='backend_api.PolicyEvaluation', to='backend_api.evaluation', verbose_name='关联评估'),
        ),
        migrations.AddField(
            model_name='policy',
            name='model',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to='backend_api.model', verbose_name='关联模型'),
        ),
        migrations.AddField(
            model_name='modelinferencelog',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作者'),
        ),
        migrations.AddField(
            model_name='modelinferencelog',
            name='training_model',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inference_logs', to='backend_api.trainingmodel', verbose_name='训练模型'),
        ),
        migrations.AddField(
            model_name='modelexportlog',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作者'),
        ),
        migrations.AddField(
            model_name='modelexportlog',
            name='training_model',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='export_logs', to='backend_api.trainingmodel', verbose_name='训练模型'),
        ),
        migrations.AddField(
            model_name='model',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='evaluationtest',
            name='evaluation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tests', to='backend_api.evaluation', verbose_name='关联评估'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='environment',
            name='creator',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='environments', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='emulator',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='dltrainingconfig',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='deduction',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='deduction',
            name='model',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='backend_api.model', verbose_name='关联的model'),
        ),
        migrations.AddField(
            model_name='deduction',
            name='task',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='deduction', to='backend_api.task', verbose_name='关联任务'),
        ),
        migrations.AddField(
            model_name='dataset',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='algorithm',
            name='creater',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AddField(
            model_name='algorithm',
            name='environment',
            field=models.ForeignKey(help_text='算法关联的仿真环境', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='algorithms', to='backend_api.environment', verbose_name='关联环境'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['task_id'], name='training_mo_task_id_db26c1_idx'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['created_by'], name='training_mo_created_deafe3_idx'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['export_status'], name='training_mo_export__355e4c_idx'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['created_time'], name='training_mo_created_a4866c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='rltrainingmetrics',
            unique_together={('task', 'episode')},
        ),
        migrations.AlterUniqueTogether(
            name='policyevaluation',
            unique_together={('policy', 'evaluation')},
        ),
        migrations.AlterUniqueTogether(
            name='environment',
            unique_together={('name', 'version')},
        ),
    ]
