import uuid
import os
from django.db import models
from django.conf import settings


class Dataset(models.Model):
    """
    数据集模型类
    """
    class DatasetTypeChoice(models.TextChoices):
        IMAGE = 'image', '图片'
        TEXT = 'text', '文本'
        VIDEO = 'video', '视频'
        OTHER = 'other', '其他'
    
    class DatasetStatusChoice(models.TextChoices):
        ENABLED = 'enabled', '启用'
        DISABLED = 'disabled', '禁用'
    
    id = models.AutoField(primary_key=True)
    dataset_id = models.CharField(max_length=64, unique=True, editable=False, verbose_name="数据集ID")
    name = models.CharField(max_length=128, verbose_name="数据集名称")
    description = models.TextField(blank=True, null=True, verbose_name="数据集描述")
    dataset_type = models.CharField(max_length=20, choices=DatasetTypeChoice.choices, default=DatasetTypeChoice.OTHER, verbose_name="数据集类型")
    file = models.FileField(upload_to="datasets", null=True, blank=True, verbose_name="数据集文件")
    address = models.CharField(max_length=255, null=True, blank=True, verbose_name="数据集存储地址")
    original_file = models.CharField(max_length=255, null=True, blank=True, verbose_name="原始文件路径")
    size = models.CharField(max_length=64, null=True, blank=True, verbose_name="数据集大小")
    sample_count = models.IntegerField(default=0, verbose_name="样本数量")
    dataset_status = models.CharField(max_length=20, choices=DatasetStatusChoice.choices, default=DatasetStatusChoice.ENABLED, verbose_name="数据集状态")
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    def save(self, *args, **kwargs):
        if not self.dataset_id:
            self.dataset_id = str(uuid.uuid4())
        super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "数据集"
        verbose_name_plural = "数据集"
        ordering = ['-created_at'] 