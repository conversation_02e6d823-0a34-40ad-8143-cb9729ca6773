import uuid
from django.db import models


class Environment(models.Model):
    """
    环境管理模型
    用于管理训练、推演、评估等任务的运行环境
    包含环境镜像文件、代码仓库地址等信息
    """
    
    class StatusChoice(models.IntegerChoices):
        """环境状态"""
        ACTIVE = 0, '可用'
        DEPRECATED = 1, '已弃用'

    # 基本信息
    env_id = models.UUIDField('环境ID', default=uuid.uuid4, editable=False)
    name = models.CharField('环境名称', max_length=255, unique=True)
    artifact_name = models.CharField('镜像名称', max_length=255, unique=True)
    description = models.TextField('环境描述', null=True, blank=True)
    
    # 版本信息
    version = models.CharField('版本号', max_length=50)
    status = models.IntegerField(
        '状态',
        choices=StatusChoice.choices,
        default=StatusChoice.ACTIVE
    )

    # 镜像信息
    image_file = models.FileField(
        '环境镜像文件',
        upload_to='environments/images',
        null=True,
        blank=True
    )
    harbor_url = models.URLField(
        'Harbor镜像地址',
        max_length=1024,
        null=True,
        blank=True,
        help_text='Harbor中存储的镜像地址'
    )

    # 代码仓库信息
    gitlab_url = models.URLField(
        'GitLab仓库地址',
        max_length=1024,
        null=True,
        blank=True,
        help_text='环境相关代码的GitLab仓库地址'
    )
    
    env_variables = models.JSONField(
        '环境变量',
        null=True,
        blank=True,
        help_text='环境变量配置，JSON格式'
    )

    # 时间信息
    create_time = models.DateTimeField('创建时间', auto_now_add=True)
    update_time = models.DateTimeField('更新时间', auto_now=True)
    deprecated_time = models.DateTimeField('弃用时间', null=True, blank=True)

    # 创建者信息
    creator = models.ForeignKey(
        'user.User',
        on_delete=models.SET_NULL,
        verbose_name='创建者',
        related_name='environments',
        null=True
    )

    def __str__(self):
        return f"{self.name} ({self.version})"
    
    def save(self, *args, **kwargs):
        # 如果状态改为已弃用，记录弃用时间
        if self.status == self.StatusChoice.DEPRECATED and not self.deprecated_time:
            from django.utils import timezone
            self.deprecated_time = timezone.now()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = '环境配置'
        verbose_name_plural = verbose_name
        ordering = ('-update_time',)
        unique_together = ('name', 'version')

    
