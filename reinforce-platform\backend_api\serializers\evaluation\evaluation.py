from rest_framework import serializers
from backend_api.models.evaluation.evaluation import Evaluation


class EvaluationSerializer(serializers.ModelSerializer):
    """
    评估序列化器
    """
    id = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Evaluation
        fields = ['id', 'name', 'type', 'description']


class EvaluationDetailSerializer(serializers.ModelSerializer):
    """
    评估详情序列化器，包含更多关联信息
    """
    id = serializers.IntegerField(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Evaluation
        fields = "__all__"