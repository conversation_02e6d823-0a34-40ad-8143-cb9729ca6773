from rest_framework import serializers
from backend_api.models.model import Model
from user.serializers.user import UserSerializer

class ModelBasicSerializer(serializers.ModelSerializer):
    """基础序列化器，只返回模型名称"""
    class Meta:
        model = Model
        fields = ['id', 'name']

class ModelSerializer(serializers.ModelSerializer):
    """模型序列化器"""
    class Meta:
        model = Model
        fields = '__all__'

class ModelDetailSerializer(serializers.ModelSerializer):
    """模型详情序列化器"""
    creater = UserSerializer()
    
    class Meta:
        model = Model
        fields = '__all__'

