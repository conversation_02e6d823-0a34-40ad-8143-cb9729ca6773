from django.db import models
import uuid


class Model(models.Model):
    
    MODEL_STATUS_CHOICES = (
        ('训练中', '训练中'),
        ('启用', '启用'),
        ('禁用', '禁用'),
        ('完成', '完成'),
        ('中断', '中断'),
        ('未知', '未知')
    )
    
    MODEL_TYPE_CHOICES = (
        ('目标检测车', '目标检测车'),
        ('野战指挥车', '野战指挥车'),
        ('远程精式火箭炮', '远程精式火箭炮'),
        ('无人机', '无人机'),
        ('智能火控', '智能火控'),
        ('无人车', '无人车'),
        ('未分类', '未分类')
    )
    
    model_id = models.UUIDField('模型id', default=uuid.uuid4, editable=False)
    name = models.CharField('模型名称', max_length=255, blank=False)
    desc = models.CharField('模型描述', max_length=1024, null=True)
    model_file = models.CharField('模型文件名', max_length=1024, null=True)
    config_file = models.CharField('模型配置文件名', max_length=1024, null=True)
    size = models.CharField('模型大小', max_length=255, null=True)
    #address = models.CharField('模型存储地址', max_length=255, null=True)
    create_time = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    model_create_time = models.DateTimeField('模型创建时间', null=True)
    model_type = models.CharField('模型类型', max_length=50, choices=MODEL_TYPE_CHOICES, default='未分类')
    model_status = models.CharField('模型状态', max_length=50, choices=MODEL_STATUS_CHOICES, default='未知')
    
    creater = models.ForeignKey('user.User', on_delete=models.CASCADE, verbose_name='创建用户', db_constraint=False, null=True)
    #task = models.ForeignKey('backend_api.Task', on_delete=models.CASCADE, related_name='models', verbose_name='关联的训练', db_constraint=False, null=True)
    
    class Meta:
        verbose_name = '模型实例'
        verbose_name_plural = verbose_name
        ordering = ('-id',)