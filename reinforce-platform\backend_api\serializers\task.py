import csv
import datetime
import json
import os
import re
import time
from django.utils import timezone
from rest_framework import serializers
from backend_api.models.model import Model
from backend_api.models.task import Task

from utils import file_util, k8s_client, ray_utils

class TaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = "__all__"
        
        
class TaskReleaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["id", "name", "status"]
        

class TaskSuspendingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["task_id", "status"]
        
        
class TaskResumeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["task_id", "status"]
        
        
class TaskLogSerializer(serializers.Serializer):
    task_id = serializers.CharField(max_length=100)
    pod_name = serializers.CharField(max_length=100)
        

class TaskListSerializer(serializers.ModelSerializer):
    
    creater_name = serializers.SerializerMethod<PERSON>ield()
    running_time = serializers.SerializerMethodField()
    tb_url = serializers.SerializerMethodField()
    specific_instance = serializers.SerializerMethodField()
    
    def get_creater_name(self, task):
        if task.owner:
            return task.owner.username
        return ""
    
    def get_tb_url(self, task):
        
        return None
    
    def get_running_time(self, task):
        
        start_time = task.start_time
        end_time = task.end_time
        
        if start_time:
            if end_time:
                running_time = end_time - start_time
                return running_time
            else:
                current_time = timezone.now()

                # 进行减法运算
                time_difference = current_time - start_time
                return time_difference
        else:
            return 0
        
    def get_specific_instance(self, task):
        if task.task_type == Task.TaskType.TRAINING:
            from backend_api.serializers.training import TrainingSerializer
            if hasattr(task, 'training'):
                return TrainingSerializer(task.training).data
        elif task.task_type == Task.TaskType.DEDUCTION:
            from backend_api.serializers.deduction import DeductionSerializer
            if hasattr(task, 'deduction'):
                return DeductionSerializer(task.deduction).data
        elif task.task_type == Task.TaskType.EVALUATION:
            from backend_api.serializers.evaluation import EvaluationSerializer
            if hasattr(task, 'evaluation'):
                return EvaluationSerializer(task.evaluation).data
        return None
    
    class Meta:
        model = Task
        fields = "__all__"


class TaskDetailSerializer(serializers.ModelSerializer):
    
    creater_name = serializers.SerializerMethodField()
    models = serializers.SerializerMethodField()
    running_status = serializers.SerializerMethodField()
    running_time = serializers.SerializerMethodField()
    specific_instance = serializers.SerializerMethodField()
    
    def get_creater_name(self, task):
        if task.owner:
            return task.owner.username
        return ""
    
    def get_specific_instance(self, task):
        if task.task_type == Task.TaskType.TRAINING:
            from backend_api.serializers.training import TrainingSerializer
            if hasattr(task, 'training'):
                return TrainingSerializer(task.training).data
        elif task.task_type == Task.TaskType.DEDUCTION:
            from backend_api.serializers.deduction import DeductionSerializer
            if hasattr(task, 'deduction'):
                return DeductionSerializer(task.deduction).data
        elif task.task_type == Task.TaskType.EVALUATION:
            from backend_api.serializers.evaluation import EvaluationSerializer
            if hasattr(task, 'evaluation'):
                return EvaluationSerializer(task.evaluation).data
        return None
    
    def get_running_status(self, task):
        try:
            rayjob = ray_utils.get_ray_job(task.name)
            if rayjob:
                rayjob_status = rayjob['status']
                ray_cluster_name = rayjob_status['rayClusterName']
                rayjob_pods = k8s_client.get_selector_pods("rl-platform", f"job-name={task.name}")
                actor_pods = k8s_client.get_selector_pods("rl-platform", f"ray.io/group=actor-group,ray.io/cluster={ray_cluster_name}")
                learner_pods = k8s_client.get_selector_pods("rl-platform", f"ray.io/group=learner-group,ray.io/cluster={ray_cluster_name}")
                
                actors = []
                learners = []
                if rayjob_pods and actor_pods and learner_pods:
                    update_time = timezone.now()
                    for actor in actor_pods.items:
                       cmd_result = k8s_client.execute_command_in_pod(actor.metadata.name, "ray-worker", "rl-platform", 
                                                                      "awk '/eth0/ {print \"{\\\"received\\\": \" $2 \", \\\"transmitted\\\": \" $10 \"}\"}' /proc/net/dev | tr -d '\n'")
                       cmd_result_json = cmd_result.replace("'", '"')
                       json_result = json.loads(cmd_result_json)
                       
                       total_bytes = json_result['received'] + json_result['transmitted']
                       
                       actors.append({"name": actor.metadata.name, "status": actor.status.phase, "speed": total_bytes})
                    for learner in learner_pods.items:
                       cmd_result = k8s_client.execute_command_in_pod(learner.metadata.name, "ray-worker", "rl-platform", 
                                                                      "awk '/eth0/ {print \"{\\\"received\\\": \" $2 \", \\\"transmitted\\\": \" $10 \"}\"}' /proc/net/dev | tr -d '\n'")
                       cmd_result_json = cmd_result.replace("'", '"')
                       json_result = json.loads(cmd_result_json)
                       
                       total_bytes = json_result['received'] + json_result['transmitted']
                       learners.append({"name": learner.metadata.name, "status": learner.status.phase, "speed": total_bytes})
                        
                    rayjob_pods = [p.metadata.name for p in rayjob_pods.items]
                
                return {"ray_cluster_name": ray_cluster_name, "rayjobs": rayjob_pods, "actors": actors, "learners": learners, "update_time": update_time}
            else:
                return {}
        except Exception as e:
            print(f"Error getting ray job status for task {task.task_id}: {e}")
            return {}
        pass
        
    
    def get_running_time(self, task):
        
        start_time = task.start_time
        end_time = task.end_time
        
        if start_time:
            if end_time:
                running_time = end_time - start_time
                return running_time
            else:
                current_time = timezone.now()

                # 进行减法运算
                time_difference = current_time - start_time
                return time_difference
        else:
            return 0

    
    def get_models(self, task):
        try:
            models = []
            # 模型路径 
            # 查找以task.name开头，后面跟着_timestamp格式的文件夹
            exp_base_dir = task.name
            task_dir = os.path.expanduser(f"common/{task.task_id}")
            if os.path.exists(task_dir):
                # 获取所有以task.name开头的文件夹
                base_dirs = [d for d in os.listdir(task_dir) if os.path.isdir(os.path.join(task_dir, d)) and d.startswith(f"{task.name}_")]
                if base_dirs:
                    # 如果找到了匹配的文件夹，使用第一个（通常只有一个）
                    base_dir = os.path.join(task_dir, base_dirs[0])
                    exp_base_dir = base_dirs[0]
                else:
                    # 如果没找到，使用默认路径
                    base_dir = os.path.join(task_dir, task.name)
            else:
                base_dir = os.path.join(task_dir, task.name)
            
            # 获取所有实验文件夹
            for exp_dir in os.listdir(base_dir):
                exp_path = os.path.join(base_dir, exp_dir)
                if not os.path.isdir(exp_path):
                    continue
                    
                # 获取checkpoint文件夹下的所有checkpoint文件
                checkpoint_dir = os.path.join(exp_path)
                if not os.path.exists(checkpoint_dir):
                    continue
                    
                checkpoints = [f for f in os.listdir(checkpoint_dir) if f.startswith('checkpoint_') and os.path.isdir(os.path.join(checkpoint_dir, f))]
                
                for ckpt in checkpoints:
                    file_path = os.path.join(checkpoint_dir, ckpt)
                    # 从文件名中提取steps (checkpoint_000038 -> 38)
                    steps = re.search(r'checkpoint_(\d+)', ckpt).group(1)
                    ctime = os.path.getctime(file_path)
                    created_time = timezone.localtime(datetime.datetime.fromtimestamp(ctime, tz=datetime.timezone.utc))
                    
                    model = {
                        "base_dir": exp_base_dir,
                        "exp_dir": exp_dir,
                        "file": ckpt,
                        "size": os.path.getsize(file_path),
                        "create_time": created_time,
                        "steps": steps,
                    }
                    models.append(model)
            
            # 按创建时间排序
            models.sort(key=lambda x: x['create_time'], reverse=True)
            return models
            
        except Exception as e:
            print(f"Error listing checkpoint files: {e}")
            return []
        
    class Meta:
        model = Task
        fields = "__all__"
