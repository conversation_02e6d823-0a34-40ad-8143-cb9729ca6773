import os
from django.http import HttpResponse
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
from backend_api.models.model import Model

from backend_api.models.scenario import Scenario
from backend_api.models.algorithm import Algorithm
from backend_api.models.task import Task
from backend_api.serializers.task import TaskListSerializer

from django.db.models import Q
from reinforce_platform import settings

from utils import k8s_client
from utils.prometheus_client import monitor_client


class OverViewAPIView(APIView):
    """
    声明用户资源类 用户操作:获取想定信息  更新想定  删除想定 创建想定
    """
    
    def get(self, request):
        user = request.user
        if user.is_staff:
            overall = {
                "scenarios": Scenario.objects.count(),
                "training": Task.objects.filter(is_deleted=False).count(),
                "models": Model.objects.count(),
                "algorithms": Algorithm.objects.count(),
            }
        else:
            overall = {
                "scenarios": Scenario.objects.filter(Q(creater=user.id) | Q(creater__is_staff=True)).count(),
                "training": Task.objects.filter(owner=user.id, is_deleted=False).count(),
                "models": Model.objects.filter(creater=user.id).count(),
                "algorithms": Algorithm.objects.filter(Q(creater=user.id) | Q(creater__is_staff=True)).count(),
            }
        
        tasks = Task.objects.filter(owner=user.id, status=Task.StatusChoice.RUNNING, is_deleted=False).all()
        for task in tasks:
            print(task.name)
        running = TaskListSerializer(tasks, many=True)
        
        saving = {}
        try:
            saving = k8s_client.get_pvc_usage("common-pvc", user.username)
        except Exception as e:
            print(f"获取PVC使用情况失败: {e}")
            saving = {}
            
        # resource = k8s_client.get_resources()
        # print(resource)
        
        # 获取集群资源信息，包含完整的异常处理
        resource = {}
        try:
            # 初始化默认值，防止监控服务连接失败
            cpus = []
            gpus = []
            memorys = []
            disks = []
            
            try:
                cpus = monitor_client.get_cluster_cpu_index()
            except Exception as e:
                print(f"获取CPU指标失败: {e}")
                cpus = []
                
            try:
                gpus = monitor_client.get_cluster_gpu_index()
            except Exception as e:
                print(f"获取GPU指标失败: {e}")
                gpus = []
                
            try:
                memorys = monitor_client.get_cluster_memory_index()
            except Exception as e:
                print(f"获取内存指标失败: {e}")
                memorys = []
                
            try:
                disks = monitor_client.get_cluster_filesystem_index()
            except Exception as e:
                print(f"获取磁盘指标失败: {e}")
                disks = []
            
            print("cpus", cpus)
            print("gpus", gpus)
            print("memorys", memorys)
            print("disks", disks)
            
            capacity = {"cpu": 0, "memory": 0, "gpu": 0, "disk": 4153975074816}
            allocatable = {"cpu": 0, "memory": 0, "gpu": 0, "disk": 2868497313792}
            usage_ratio = {"cpu": 0, "memory": 0, "gpu": 0, "disk": 0.3094578515969888}
            
            # 安全地处理GPU指标数据
            try:
                if len(gpus) >= 3:
                    capacity["gpu"] = int(float(gpus[2]["value"])) if gpus[2].get("value") else 0
                    allocatable["gpu"] = (int(float(gpus[2]["value"])) - int(float(gpus[1]["value"]))) if gpus[2].get("value") and gpus[1].get("value") else 0
                    usage_ratio["gpu"] = float(gpus[0]["value"]) if gpus[0].get("value") else 0
            except (ValueError, KeyError, IndexError) as e:
                print(f"处理GPU指标数据时出错: {e}")
            
            # 安全地处理CPU指标数据
            try:
                if len(cpus) >= 3:
                    capacity["cpu"] = int(float(cpus[2]["value"])) if cpus[2].get("value") else 0
                    allocatable["cpu"] = int(float(cpus[2]["value"])) - round(float(cpus[1]["value"])) if cpus[2].get("value") and cpus[1].get("value") else 0
                    usage_ratio["cpu"] = float(cpus[0]["value"]) if cpus[0].get("value") else 0
            except (ValueError, KeyError, IndexError) as e:
                print(f"处理CPU指标数据时出错: {e}")
                
            # 安全地处理内存指标数据
            try:
                if len(memorys) >= 3:
                    capacity["memory"] = int(float(memorys[2]["value"])) if memorys[2].get("value") else 0
                    allocatable["memory"] = int(float(memorys[2]["value"])) - int(float(memorys[1]["value"])) if memorys[2].get("value") and memorys[1].get("value") else 0
                    usage_ratio["memory"] = float(memorys[0]["value"]) if memorys[0].get("value") else 0
            except (ValueError, KeyError, IndexError) as e:
                print(f"处理内存指标数据时出错: {e}")
                
            # 安全地处理磁盘指标数据
            try:
                if len(disks) >= 3:
                    capacity["disk"] = int(float(disks[2]["value"])) if disks[2].get("value") else capacity["disk"]
                    allocatable["disk"] = int(float(disks[2]["value"])) - int(float(disks[1]["value"])) if disks[2].get("value") and disks[1].get("value") else allocatable["disk"]
                    usage_ratio["disk"] = float(disks[0]["value"]) if disks[0].get("value") else usage_ratio["disk"]
            except (ValueError, KeyError, IndexError) as e:
                print(f"处理磁盘指标数据时出错: {e}")
            
            resource = {"capacity": capacity, "allocatable": allocatable, "usage_ratio": usage_ratio}
            
        except Exception as e:
            print(f"获取集群资源信息时发生未预期错误: {e}")
            # 返回默认的资源信息
            resource = {
                "capacity": {"cpu": 0, "memory": 0, "gpu": 0, "disk": 4153975074816},
                "allocatable": {"cpu": 0, "memory": 0, "gpu": 0, "disk": 2868497313792},
                "usage_ratio": {"cpu": 0, "memory": 0, "gpu": 0, "disk": 0.3094578515969888}
            }
        
        data = {"overall": overall, "running": running.data, "saving": saving, "resource": resource}
        
        return Response({"code": 200, "data": data}, status=HTTP_200_OK)
        
        
class DownloadsAPIView(APIView):
    
    def post(self, request):
        file_name = request.data.get('file_name')
        if not file_name:
            return Response({"code": 400, "msg": "file_name is required."}, status=HTTP_400_BAD_REQUEST)
        file_path = os.path.join(settings.MEDIA_ROOT, file_name)
        
        print(file_path)
        if not os.path.exists(file_path):
            return Response({"code": 400, "msg": "file not found."}, status=HTTP_400_BAD_REQUEST)

        with open(file_path, 'rb') as file:
            response = HttpResponse(file.read(), content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            return response