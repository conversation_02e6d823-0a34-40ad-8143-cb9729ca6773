import os
import logging
from rest_framework.response import Response
from rest_framework.parsers import Multi<PERSON>art<PERSON>ars<PERSON>
from rest_framework.viewsets import ModelViewSet
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from rest_framework.decorators import action
from django.db import transaction
import uuid

from backend_api.models.algorithm import Algorithm
from backend_api.serializers.algorithm import AlgorithmSerializer
from utils.gitlab_api import GitlabAPI
from utils import k8s_client, task_utils

logger = logging.getLogger(__name__)

class AlgorithmViewSets(ModelViewSet):
    """
    声明用户资源类 用户操作:获取算法信息  更新算法  删除算法 创建算法
    """
    queryset = Algorithm.objects.all()
    serializer_class = AlgorithmSerializer
    filter_backends = [DjangoFilterBackend]
    
    def get_queryset(self):
        try:
            user = self.request.user
            if user.is_staff:
                name = self.request.query_params.get('name', '')
                if name and name != '':
                    queryset = Algorithm.objects.filter(name__icontains=name)
                else:
                    queryset = Algorithm.objects.all()
            else:
                query = Q(creater=user.id) | Q(creater__is_staff=True)
                name = self.request.query_params.get('name', '')
                if name and name != '':
                    queryset = Algorithm.objects.filter(creater=user.id).filter(name__icontains=name)
                else:
                    queryset = Algorithm.objects.filter(query)
            return queryset.select_related('environment', 'creater')
        except Exception as e:
            logger.error(f"Error in get_queryset: {str(e)}", exc_info=True)
            return Algorithm.objects.none()

    def list(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                queryset = self.filter_queryset(self.get_queryset())
                
                # 检查并修复无效的 UUID
                for algo in queryset:
                    if algo.algorithm_id:
                        try:
                            # 验证 UUID 格式
                            uuid.UUID(str(algo.algorithm_id))
                        except (ValueError, AttributeError, TypeError):
                            # 如果无效，生成新的 UUID
                            algo.algorithm_id = uuid.uuid4()
                            algo.save(update_fields=['algorithm_id'])
                            logger.warning(f"Fixed invalid UUID for algorithm {algo.id}")
                
                page = self.paginate_queryset(queryset)
                if page is not None:
                    serializer = self.get_serializer(page, many=True)
                    return self.get_paginated_response(serializer.data)

                serializer = self.get_serializer(queryset, many=True)
                return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in list view: {str(e)}", exc_info=True)
            return Response({
                "err_msg": "获取算法列表失败",
                "msg": str(e),
                "code": 500
            }, status=HTTP_400_BAD_REQUEST)
    
    def create(self, request):
        user = request.user
        request_data = request.data.copy()
        request_data["creater"] = user.id
        name = request.data["name"]
        if Algorithm.objects.filter(name=name).count() > 0:
            return Response({"err_msg": f"algorithm {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        try:
            # 使用 GitlabAPI 创建项目
            gitlab_api = GitlabAPI()
            project = gitlab_api.fork_project(
                name=request.data['artifact_name'],
                proj_id=4,
                description=f"算法 {request.data['name']} 的代码仓库"
            )
            
            # 设置 GitLab URL
            if not request.data.get('gitlab_url'):
                request_data['gitlab_url'] = project.get('web_url')
                
        except Exception as e:
            return Response({
                "err_msg": f"创建 GitLab 项目异常: {str(e)}",
                "msg": "GitLab API 异常",
                "code": 500
            }, status=HTTP_400_BAD_REQUEST)
            
        seria = self.get_serializer(data=request_data)
        if seria.is_valid():
            algo_inst = seria.save()
            return Response({"data": self.get_serializer(algo_inst).data, "msg": "create algorithm success.", "code": 200}, status=HTTP_201_CREATED)
        else:
            return Response({"err_msg": seria.errors, "msg": "参数错误", "code": 401}, status=HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def start_webide(self, request, pk=None):
        """启动 WebIDE 进行算法调试"""
        try:
            # 获取算法实例
            algorithm = self.get_object()
            
            # 检查算法是否存在
            if not algorithm:
                return Response({
                    "err_msg": "算法不存在",
                    "msg": "对象不存在错误",
                    "code": "object_not_exists"
                }, status=HTTP_400_BAD_REQUEST)
            
            # 检查是否有 GitLab URL
            if not algorithm.gitlab_url:
                return Response({
                    "err_msg": "算法没有关联的 GitLab 仓库",
                    "msg": "参数错误",
                    "code": "missing_gitlab_url"
                }, status=HTTP_400_BAD_REQUEST)
            
            # 生成唯一的服务名称
            unique_id = str(uuid.uuid4())[:4]
            svc_name = f"webide-{algorithm.artifact_name.lower()}-{unique_id}"
            app_name = f"webide-{algorithm.artifact_name.lower()}-{unique_id}"
            
            # 启动 WebIDE 服务
            service = k8s_client.create_webide_service(
                namespace="rl-platform",
                image_name="core.**************.nip.io:30670/library/ppo-aircombat1v1:latest",
                svc_name=svc_name,
                app_name=app_name,
                gitlab_url=algorithm.gitlab_url
            )
            
            # 获取服务的 NodePort
            node_port = None
            if service and service.spec and service.spec.ports:
                for port in service.spec.ports:
                    if port.node_port:
                        node_port = port.node_port
                        break
            
            if not node_port:
                return Response({
                    "err_msg": "无法获取服务端口",
                    "msg": "服务创建错误",
                    "code": 500
                }, status=HTTP_400_BAD_REQUEST)
            
            return Response({
                "msg": "WebIDE 服务启动成功",
                "code": 200,
                "data": {
                    "service_name": svc_name,
                    "app_name": app_name,
                    "node_port": node_port,
                    "url": f"http://*************:{node_port}"  # 使用实际的节点 IP
                }
            }, status=HTTP_200_OK)
            
        except Exception as e:
            return Response({
                "err_msg": f"启动 WebIDE 失败: {str(e)}",
                "msg": "服务启动错误",
                "code": 500
            }, status=HTTP_400_BAD_REQUEST)