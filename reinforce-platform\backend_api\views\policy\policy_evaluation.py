from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
from django_filters.rest_framework import DjangoFilterBackend

from backend_api.models.policy.policy import Policy
from backend_api.models.policy.policy_evaluation import PolicyEvaluation
from backend_api.serializers.policy.policy_evaluation import PolicyEvaluationSerializer


class PolicyEvaluationViewSets(ModelViewSet):
    """
    策略评估视图集，提供策略评估管理的API接口
    包括：获取评估信息、运行评估、应用评估结果等
    """
    queryset = PolicyEvaluation.objects.all()
    serializer_class = PolicyEvaluationSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['policy', 'evaluation', 'status']
    
    def get_queryset(self):
        """获取查询集，可根据用户权限过滤"""
        user = self.request.user
        queryset = self.queryset
        
        # 根据策略ID过滤
        policy_id = self.request.query_params.get('policy_id', '')
        if policy_id:
            queryset = queryset.filter(policy__policy_id=policy_id)
            
        # 根据评估ID过滤
        evaluation_id = self.request.query_params.get('evaluation_id', '')
        if evaluation_id:
            queryset = queryset.filter(evaluation__evaluation_id=evaluation_id)
            
        # 如果不是管理员，只能看到自己创建的策略的评估
        if not user.is_staff:
            queryset = queryset.filter(policy__creater=user.id)
            
        return queryset
    
    def list(self, request, *args, **kwargs):
        """获取策略评估列表"""
        queryset = self.filter_queryset(self.get_queryset())
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
    
    @action(detail=True, methods=['post'])
    def run_evaluation(self, request, pk=None):
        """运行策略评估"""
        policy_evaluation = self.get_object()
        
        # 检查评估状态
        if policy_evaluation.status == PolicyEvaluation.StatusChoice.RUNNING:
            return Response(
                {"err_msg": "评估正在运行中", "msg": "状态错误", "code": "evaluation_running"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 检查策略状态
        policy = policy_evaluation.policy
        if policy.status != Policy.StatusChoice.ACTIVE:
            return Response(
                {"err_msg": "只有状态为'可用'的策略才能进行评估", "msg": "策略状态错误", "code": "policy_status_error"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 更新评估状态
        policy_evaluation.status = PolicyEvaluation.StatusChoice.RUNNING
        policy_evaluation.save()
        
        # TODO: 实际评估运行逻辑，可以在这里调用异步任务
        # 此处应该调用Celery任务来执行评估
        
        return Response(
            {"msg": "评估已开始运行", "code": 200, "data": self.get_serializer(policy_evaluation).data},
            status=HTTP_200_OK
        )
    
    @action(detail=True, methods=['post'])
    def apply_result(self, request, pk=None):
        """应用评估结果到策略特征标签"""
        policy_evaluation = self.get_object()
        
        # 检查评估状态
        if policy_evaluation.status != PolicyEvaluation.StatusChoice.COMPLETED:
            return Response(
                {"err_msg": "只有完成的评估结果才能应用", "msg": "评估状态错误", "code": "evaluation_status_error"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 检查是否已应用
        if policy_evaluation.is_applied:
            return Response(
                {"err_msg": "评估结果已应用", "msg": "操作重复", "code": "already_applied"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 获取策略和评估结果
        policy = policy_evaluation.policy
        result = policy_evaluation.result
        
        if not result:
            return Response(
                {"err_msg": "评估结果为空", "msg": "结果错误", "code": "empty_result"}, 
                status=HTTP_400_BAD_REQUEST
            )
        
        # 更新策略特征标签
        if not policy.features:
            policy.features = {}
            
        # 将评估结果中的特征添加到策略特征标签中
        if 'features' in result:
            for key, value in result['features'].items():
                policy.features[key] = value
                
        # 更新ELO评分
        if policy_evaluation.elo_rating:
            policy.features['elo_rating'] = policy_evaluation.elo_rating
            
        policy.save()
        
        # 标记为已应用
        policy_evaluation.is_applied = True
        policy_evaluation.save()
        
        return Response(
            {"msg": "评估结果已应用到策略特征标签", "code": 200},
            status=HTTP_200_OK
        )